/**
 * 底部导航可见性管理Hook
 * 确保底部导航在React组件挂载后立即可见
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Capacitor } from '@capacitor/core';

interface BottomNavVisibilityState {
  isVisible: boolean;
  isMounted: boolean;
  hasContent: boolean;
  renderAttempts: number;
  lastError?: string;
}

interface BottomNavVisibilityConfig {
  forceVisible: boolean;
  enableAutoFix: boolean;
  maxRenderAttempts: number;
  checkInterval: number;
}

/**
 * 底部导航可见性管理Hook - 重构版，解决循环依赖问题
 */
export const useBottomNavVisibility = (
  config: Partial<BottomNavVisibilityConfig> = {}
): BottomNavVisibilityState & {
  forceVisible: () => void;
  checkVisibility: () => boolean;
  ensureVisible: () => Promise<boolean>;
} => {
  const finalConfig: BottomNavVisibilityConfig = {
    forceVisible: true,
    enableAutoFix: true,
    maxRenderAttempts: 5,
    checkInterval: 100,
    ...config
  };

  // 🔧 使用useRef避免循环依赖
  const stateRef = useRef<BottomNavVisibilityState>({
    isVisible: false,
    isMounted: false,
    hasContent: false,
    renderAttempts: 0
  });

  const [, forceUpdate] = useState({});
  const elementRef = useRef<HTMLElement | null>(null);
  const checkTimeoutRef = useRef<NodeJS.Timeout>();
  const routeListenersRegistered = useRef(false);
  const lastRouteCheck = useRef(0);
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  // 🔧 强制组件重新渲染的函数
  const triggerUpdate = useCallback(() => {
    forceUpdate({});
  }, []);

  /**
   * 检查元素可见性 - 增强版，添加详细日志
   */
  const checkVisibility = useCallback((): boolean => {
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    elementRef.current = element;

    console.log('🔍 === 底部导航可见性详细检查 ===');

    if (!element) {
      console.log('❌ 底部导航元素未找到');
      console.log('📋 DOM查询结果:', {
        querySelector: !!document.querySelector('.bottom-navigation'),
        querySelectorAll: document.querySelectorAll('.bottom-navigation').length,
        getElementById: !!document.getElementById('bottom-navigation'),
        bodyChildren: document.body.children.length,
        documentReady: document.readyState
      });

      stateRef.current = {
        ...stateRef.current,
        isVisible: false,
        isMounted: false,
        hasContent: false,
        renderAttempts: stateRef.current.renderAttempts + 1
      };
      triggerUpdate();
      return false;
    }

    const style = getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    // 🔍 详细的CSS属性检查
    const cssProperties = {
      display: style.display,
      visibility: style.visibility,
      opacity: style.opacity,
      zIndex: style.zIndex,
      position: style.position,
      top: style.top,
      bottom: style.bottom,
      left: style.left,
      right: style.right,
      width: style.width,
      height: style.height,
      transform: style.transform,
      backgroundColor: style.backgroundColor,
      borderTop: style.borderTop
    };

    // 🔍 详细的尺寸和位置检查
    const geometryInfo = {
      boundingRect: {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        top: rect.top,
        bottom: rect.bottom,
        left: rect.left,
        right: rect.right
      },
      offsetDimensions: {
        offsetWidth: element.offsetWidth,
        offsetHeight: element.offsetHeight,
        offsetTop: element.offsetTop,
        offsetLeft: element.offsetLeft
      },
      clientDimensions: {
        clientWidth: element.clientWidth,
        clientHeight: element.clientHeight,
        clientTop: element.clientTop,
        clientLeft: element.clientLeft
      },
      scrollDimensions: {
        scrollWidth: element.scrollWidth,
        scrollHeight: element.scrollHeight,
        scrollTop: element.scrollTop,
        scrollLeft: element.scrollLeft
      }
    };

    // 🔍 DOM结构检查
    const domInfo = {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      childrenCount: element.children.length,
      parentElement: element.parentElement?.tagName || 'none',
      nextSibling: element.nextElementSibling?.tagName || 'none',
      previousSibling: element.previousElementSibling?.tagName || 'none',
      attributes: Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`),
      innerHTML: element.innerHTML.substring(0, 100) + (element.innerHTML.length > 100 ? '...' : '')
    };

    // 检查基础可见性
    const isDisplayed = style.display !== 'none';
    const isVisible = style.visibility !== 'hidden';
    const hasOpacity = parseFloat(style.opacity) > 0;
    const hasSize = rect.width > 0 && rect.height > 0;
    const hasChildren = element.children.length > 0;
    const inViewport = rect.top < window.innerHeight && rect.bottom > 0;

    const visible = isDisplayed && isVisible && hasOpacity && hasSize && inViewport;

    console.log('📊 CSS属性详情:', cssProperties);
    console.log('📐 几何信息详情:', geometryInfo);
    console.log('🏗️ DOM结构详情:', domInfo);
    console.log('✅ 可见性判断:', {
      isDisplayed,
      isVisible,
      hasOpacity,
      hasSize,
      hasChildren,
      inViewport,
      finalResult: visible
    });

    // 🔧 更新状态
    stateRef.current = {
      ...stateRef.current,
      isVisible: visible,
      isMounted: !!element,
      hasContent: hasChildren,
      renderAttempts: stateRef.current.renderAttempts + 1
    };
    triggerUpdate();

    return visible;
  }, [triggerUpdate]);

  /**
   * 强制设置可见性 - 增强版，多重保障
   */
  const forceVisible = useCallback(() => {
    const element = elementRef.current || document.querySelector('.bottom-navigation') as HTMLElement;

    if (!element) {
      console.warn('⚠️ 底部导航元素未找到，无法强制设置可见性');
      console.log('🔍 尝试查找其他可能的选择器...');

      // 尝试其他可能的选择器
      const alternatives = [
        'nav[class*="bottom"]',
        '[class*="bottom-nav"]',
        '[class*="navigation"]',
        'footer',
        '.bottom-navigation'
      ];

      alternatives.forEach(selector => {
        const found = document.querySelector(selector);
        console.log(`   ${selector}: ${found ? '✅ 找到' : '❌ 未找到'}`);
        if (found) {
          console.log(`     标签: ${found.tagName}, 类名: ${found.className}`);
        }
      });

      return;
    }

    console.log('🔧 === 强制设置底部导航可见性 ===');
    console.log('📍 目标元素:', {
      tagName: element.tagName,
      className: element.className,
      id: element.id
    });

    // 🔧 记录设置前的状态
    const beforeStyles = {
      display: getComputedStyle(element).display,
      visibility: getComputedStyle(element).visibility,
      opacity: getComputedStyle(element).opacity,
      zIndex: getComputedStyle(element).zIndex,
      position: getComputedStyle(element).position
    };
    console.log('📊 设置前样式:', beforeStyles);

    // 🔧 强制设置关键样式 - 多重方法
    const criticalStyles = {
      'display': 'block',
      'visibility': 'visible',
      'opacity': '1',
      'position': 'fixed',
      'bottom': '0',
      'left': '0',
      'right': '0',
      'z-index': '99999', // 更高的z-index
      'width': '100vw',
      'height': 'calc(60px + env(safe-area-inset-bottom, 34px))',
      'background-color': 'var(--bg-surface, #ffffff)',
      'border-top': '1px solid var(--border-color, #e5e7eb)',
      'pointer-events': 'auto'
    };

    // 方法1：使用setProperty with important
    Object.entries(criticalStyles).forEach(([property, value]) => {
      element.style.setProperty(property, value, 'important');
    });

    // 方法2：直接设置style属性
    element.style.cssText += `
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      z-index: 99999 !important;
      position: fixed !important;
    `;

    // 方法3：添加强制可见的CSS类
    element.classList.add('force-visible-bottom-nav');

    // 🔧 添加标记属性
    element.setAttribute('data-react-mounted', 'true');
    element.setAttribute('data-visibility-forced', 'true');
    element.setAttribute('data-force-timestamp', Date.now().toString());

    // 🔧 记录设置后的状态
    const afterStyles = {
      display: getComputedStyle(element).display,
      visibility: getComputedStyle(element).visibility,
      opacity: getComputedStyle(element).opacity,
      zIndex: getComputedStyle(element).zIndex,
      position: getComputedStyle(element).position
    };
    console.log('📊 设置后样式:', afterStyles);

    // 🔧 验证样式是否生效
    const styleChanges = Object.keys(beforeStyles).map(key => ({
      property: key,
      before: beforeStyles[key as keyof typeof beforeStyles],
      after: afterStyles[key as keyof typeof afterStyles],
      changed: beforeStyles[key as keyof typeof beforeStyles] !== afterStyles[key as keyof typeof afterStyles]
    }));
    console.log('🔄 样式变化对比:', styleChanges);

    console.log('✅ 底部导航可见性强制设置完成');

    // 🔧 延迟重新检查可见性
    setTimeout(() => {
      console.log('🔍 强制设置后重新检查可见性...');
      checkVisibility();
    }, 100);
  }, [checkVisibility]);

  /**
   * 确保元素可见（异步版本）
   */
  const ensureVisible = useCallback(async (): Promise<boolean> => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = finalConfig.maxRenderAttempts;

      const checkAndFix = () => {
        attempts++;
        const isVisible = checkVisibility();

        if (isVisible) {
          console.log(`✅ 底部导航可见性确认，尝试${attempts}次`);
          resolve(true);
          return;
        }

        if (attempts >= maxAttempts) {
          console.warn(`⚠️ 底部导航可见性确保失败，已尝试${attempts}次`);
          setState(prev => ({
            ...prev,
            lastError: `可见性确保失败，已尝试${attempts}次`
          }));
          resolve(false);
          return;
        }

        // 如果启用自动修复，尝试强制可见
        if (finalConfig.enableAutoFix) {
          forceVisible();
        }

        // 继续检查
        setTimeout(checkAndFix, finalConfig.checkInterval);
      };

      checkAndFix();
    });
  }, [checkVisibility, forceVisible, finalConfig.maxRenderAttempts, finalConfig.checkInterval, finalConfig.enableAutoFix]);

  /**
   * 组件挂载后的初始化 - 修复版，避免循环依赖
   */
  useEffect(() => {
    if (!isIOSDevice || !isNative) {
      // 非iOS设备直接标记为可见
      console.log('🔍 非iOS设备，直接标记为可见');
      stateRef.current = {
        ...stateRef.current,
        isVisible: true,
        isMounted: true,
        hasContent: true
      };
      triggerUpdate();
      return;
    }

    console.log('🔍 === 初始化底部导航可见性检查 ===');
    console.log('📱 设备信息:', {
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      userAgent: navigator.userAgent
    });

    // 🔧 延迟检查，确保DOM已准备好
    const initialCheck = setTimeout(() => {
      console.log('🔍 执行初始可见性检查...');
      const isVisible = checkVisibility();

      if (!isVisible && finalConfig.forceVisible) {
        console.log('⚠️ 底部导航初始不可见，启动确保可见流程...');
        ensureVisible().then(success => {
          console.log(`🎯 确保可见流程完成: ${success ? '成功' : '失败'}`);
        });
      } else if (isVisible) {
        console.log('✅ 底部导航初始状态可见');
      }
    }, 200); // 增加延迟确保React渲染完成

    // 🔧 定期检查可见性 - 使用ref避免依赖循环
    let autoFixInterval: NodeJS.Timeout | undefined;
    if (finalConfig.enableAutoFix) {
      autoFixInterval = setInterval(() => {
        const currentAttempts = stateRef.current.renderAttempts;
        if (currentAttempts < finalConfig.maxRenderAttempts) {
          const isVisible = checkVisibility();
          if (!isVisible) {
            console.log(`🔧 定期检查：检测到底部导航不可见，尝试修复... (第${currentAttempts + 1}次)`);
            forceVisible();
          }
        } else {
          console.log('⚠️ 已达到最大修复尝试次数，停止自动修复');
          if (autoFixInterval) {
            clearInterval(autoFixInterval);
          }
        }
      }, finalConfig.checkInterval * 3); // 增加间隔避免过于频繁
    }

    return () => {
      console.log('🧹 清理初始化定时器...');
      clearTimeout(initialCheck);
      if (autoFixInterval) {
        clearInterval(autoFixInterval);
      }
    };
  }, [isIOSDevice, isNative]); // 🔧 只依赖不变的值

  /**
   * 监听路由变化 - 修复版，防止重复注册
   */
  useEffect(() => {
    // 🔧 防止重复注册
    if (routeListenersRegistered.current) {
      console.log('🔄 路由监听器已注册，跳过重复注册');
      return;
    }

    console.log('🔄 === 注册路由变化监听器 ===');

    const handleRouteChange = () => {
      const now = Date.now();
      // 🔧 防抖：100ms内的重复调用将被忽略
      if (now - lastRouteCheck.current < 100) {
        console.log('🔄 路由变化防抖，跳过重复调用');
        return;
      }
      lastRouteCheck.current = now;

      console.log('🔄 检测到路由变化，重新检查底部导航可见性...');
      console.log('📍 当前路径:', window.location.pathname);

      setTimeout(() => {
        const isVisible = checkVisibility();
        if (!isVisible && finalConfig.forceVisible) {
          console.log('🔧 路由变化后导航不可见，强制设置可见性...');
          forceVisible();
        }
      }, 100); // 增加延迟确保DOM更新完成
    };

    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', handleRouteChange);

    // 🔧 保存原始方法的引用
    const originalPushState = history.pushState.bind(history);
    const originalReplaceState = history.replaceState.bind(history);

    // 监听pushstate和replacestate（程序化导航）
    history.pushState = function(state, title, url) {
      originalPushState(state, title, url);
      console.log('🔄 pushState调用:', url);
      handleRouteChange();
    };

    history.replaceState = function(state, title, url) {
      originalReplaceState(state, title, url);
      console.log('🔄 replaceState调用:', url);
      handleRouteChange();
    };

    routeListenersRegistered.current = true;
    console.log('✅ 路由监听器注册完成');

    return () => {
      console.log('🧹 清理路由监听器...');
      window.removeEventListener('popstate', handleRouteChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
      routeListenersRegistered.current = false;
    };
  }, []); // 🔧 移除依赖项，只在组件挂载时注册一次

  return {
    ...stateRef.current,
    forceVisible,
    checkVisibility,
    ensureVisible
  };
};

/**
 * 底部导航强制可见性组件
 * 用于包装底部导航，确保其始终可见
 */
export const BottomNavVisibilityEnsurer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isVisible, ensureVisible } = useBottomNavVisibility({
    forceVisible: true,
    enableAutoFix: true
  });

  useEffect(() => {
    // 组件挂载后立即确保可见性
    ensureVisible();
  }, [ensureVisible]);

  // 在开发环境显示可见性状态
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📱 底部导航可见性状态:', { isVisible });
    }
  }, [isVisible]);

  return (
    <div 
      className="bottom-nav-visibility-ensurer"
      data-visible={isVisible}
      style={{
        position: 'relative',
        zIndex: 9999
      }}
    >
      {children}
    </div>
  );
};

/**
 * 便捷函数：立即强制底部导航可见
 */
export const forceBottomNavVisible = (): void => {
  const element = document.querySelector('.bottom-navigation') as HTMLElement;
  
  if (!element) {
    console.warn('⚠️ 底部导航元素未找到');
    return;
  }

  const criticalStyles = {
    'display': 'block',
    'visibility': 'visible',
    'opacity': '1',
    'position': 'fixed',
    'bottom': '0',
    'z-index': '9999'
  };

  Object.entries(criticalStyles).forEach(([property, value]) => {
    element.style.setProperty(property, value, 'important');
  });

  console.log('✅ 底部导航强制可见完成');
};
