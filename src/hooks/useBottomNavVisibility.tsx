/**
 * 底部导航可见性管理Hook
 * 确保底部导航在React组件挂载后立即可见
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Capacitor } from '@capacitor/core';

interface BottomNavVisibilityState {
  isVisible: boolean;
  isMounted: boolean;
  hasContent: boolean;
  renderAttempts: number;
  lastError?: string;
}

interface BottomNavVisibilityConfig {
  forceVisible: boolean;
  enableAutoFix: boolean;
  maxRenderAttempts: number;
  checkInterval: number;
}

/**
 * 底部导航可见性管理Hook
 */
export const useBottomNavVisibility = (
  config: Partial<BottomNavVisibilityConfig> = {}
): BottomNavVisibilityState & {
  forceVisible: () => void;
  checkVisibility: () => boolean;
  ensureVisible: () => Promise<boolean>;
} => {
  const finalConfig: BottomNavVisibilityConfig = {
    forceVisible: true,
    enableAutoFix: true,
    maxRenderAttempts: 5,
    checkInterval: 100,
    ...config
  };

  const [state, setState] = useState<BottomNavVisibilityState>({
    isVisible: false,
    isMounted: false,
    hasContent: false,
    renderAttempts: 0
  });

  const elementRef = useRef<HTMLElement | null>(null);
  const checkTimeoutRef = useRef<NodeJS.Timeout>();
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  /**
   * 检查元素可见性
   */
  const checkVisibility = useCallback((): boolean => {
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    elementRef.current = element;

    if (!element) {
      return false;
    }

    const style = getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    // 检查基础可见性
    const isDisplayed = style.display !== 'none';
    const isVisible = style.visibility !== 'hidden';
    const hasOpacity = parseFloat(style.opacity) > 0;
    const hasSize = rect.width > 0 && rect.height > 0;
    const hasChildren = element.children.length > 0;

    const visible = isDisplayed && isVisible && hasOpacity && hasSize;

    setState(prev => ({
      ...prev,
      isVisible: visible,
      isMounted: !!element,
      hasContent: hasChildren,
      renderAttempts: prev.renderAttempts + 1
    }));

    return visible;
  }, []);

  /**
   * 强制设置可见性
   */
  const forceVisible = useCallback(() => {
    const element = elementRef.current || document.querySelector('.bottom-navigation') as HTMLElement;
    
    if (!element) {
      console.warn('⚠️ 底部导航元素未找到，无法强制设置可见性');
      return;
    }

    console.log('🔧 强制设置底部导航可见性...');

    // 强制设置关键样式
    const criticalStyles = {
      'display': 'block',
      'visibility': 'visible',
      'opacity': '1',
      'position': 'fixed',
      'bottom': '0',
      'left': '0',
      'right': '0',
      'z-index': '9999',
      'width': '100vw',
      'height': 'calc(60px + env(safe-area-inset-bottom, 34px))'
    };

    Object.entries(criticalStyles).forEach(([property, value]) => {
      element.style.setProperty(property, value, 'important');
    });

    // 添加React挂载标记
    element.setAttribute('data-react-mounted', 'true');
    element.setAttribute('data-visibility-forced', 'true');

    console.log('✅ 底部导航可见性强制设置完成');

    // 重新检查可见性
    setTimeout(() => {
      checkVisibility();
    }, 50);
  }, [checkVisibility]);

  /**
   * 确保元素可见（异步版本）
   */
  const ensureVisible = useCallback(async (): Promise<boolean> => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = finalConfig.maxRenderAttempts;

      const checkAndFix = () => {
        attempts++;
        const isVisible = checkVisibility();

        if (isVisible) {
          console.log(`✅ 底部导航可见性确认，尝试${attempts}次`);
          resolve(true);
          return;
        }

        if (attempts >= maxAttempts) {
          console.warn(`⚠️ 底部导航可见性确保失败，已尝试${attempts}次`);
          setState(prev => ({
            ...prev,
            lastError: `可见性确保失败，已尝试${attempts}次`
          }));
          resolve(false);
          return;
        }

        // 如果启用自动修复，尝试强制可见
        if (finalConfig.enableAutoFix) {
          forceVisible();
        }

        // 继续检查
        setTimeout(checkAndFix, finalConfig.checkInterval);
      };

      checkAndFix();
    });
  }, [checkVisibility, forceVisible, finalConfig.maxRenderAttempts, finalConfig.checkInterval, finalConfig.enableAutoFix]);

  /**
   * 组件挂载后的初始化
   */
  useEffect(() => {
    if (!isIOSDevice || !isNative) {
      // 非iOS设备直接标记为可见
      setState(prev => ({
        ...prev,
        isVisible: true,
        isMounted: true,
        hasContent: true
      }));
      return;
    }

    console.log('🔍 初始化底部导航可见性检查...');

    // 延迟检查，确保DOM已准备好
    const initialCheck = setTimeout(() => {
      const isVisible = checkVisibility();
      
      if (!isVisible && finalConfig.forceVisible) {
        console.log('⚠️ 底部导航初始不可见，启动确保可见流程...');
        ensureVisible();
      }
    }, 100);

    // 定期检查可见性
    if (finalConfig.enableAutoFix) {
      checkTimeoutRef.current = setInterval(() => {
        const isVisible = checkVisibility();
        if (!isVisible && state.renderAttempts < finalConfig.maxRenderAttempts) {
          console.log(`🔧 检测到底部导航不可见，尝试修复... (第${state.renderAttempts + 1}次)`);
          forceVisible();
        }
      }, finalConfig.checkInterval * 2);
    }

    return () => {
      clearTimeout(initialCheck);
      if (checkTimeoutRef.current) {
        clearInterval(checkTimeoutRef.current);
      }
    };
  }, [isIOSDevice, isNative, finalConfig, checkVisibility, forceVisible, ensureVisible, state.renderAttempts]);

  /**
   * 监听路由变化，确保导航栏在页面切换后仍然可见
   */
  useEffect(() => {
    const handleRouteChange = () => {
      console.log('🔄 检测到路由变化，重新检查底部导航可见性...');
      setTimeout(() => {
        const isVisible = checkVisibility();
        if (!isVisible && finalConfig.forceVisible) {
          forceVisible();
        }
      }, 50);
    };

    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', handleRouteChange);

    // 监听pushstate和replacestate（程序化导航）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      handleRouteChange();
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      handleRouteChange();
    };

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, [checkVisibility, forceVisible, finalConfig.forceVisible]);

  return {
    ...state,
    forceVisible,
    checkVisibility,
    ensureVisible
  };
};

/**
 * 底部导航强制可见性组件
 * 用于包装底部导航，确保其始终可见
 */
export const BottomNavVisibilityEnsurer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isVisible, ensureVisible } = useBottomNavVisibility({
    forceVisible: true,
    enableAutoFix: true
  });

  useEffect(() => {
    // 组件挂载后立即确保可见性
    ensureVisible();
  }, [ensureVisible]);

  // 在开发环境显示可见性状态
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📱 底部导航可见性状态:', { isVisible });
    }
  }, [isVisible]);

  return (
    <div 
      className="bottom-nav-visibility-ensurer"
      data-visible={isVisible}
      style={{
        position: 'relative',
        zIndex: 9999
      }}
    >
      {children}
    </div>
  );
};

/**
 * 便捷函数：立即强制底部导航可见
 */
export const forceBottomNavVisible = (): void => {
  const element = document.querySelector('.bottom-navigation') as HTMLElement;
  
  if (!element) {
    console.warn('⚠️ 底部导航元素未找到');
    return;
  }

  const criticalStyles = {
    'display': 'block',
    'visibility': 'visible',
    'opacity': '1',
    'position': 'fixed',
    'bottom': '0',
    'z-index': '9999'
  };

  Object.entries(criticalStyles).forEach(([property, value]) => {
    element.style.setProperty(property, value, 'important');
  });

  console.log('✅ 底部导航强制可见完成');
};
