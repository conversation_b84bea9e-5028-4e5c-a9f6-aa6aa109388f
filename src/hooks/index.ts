/**
 * 🧩 iOS主题与布局统一系统 - Hook模块导出入口
 * 
 * 模块化设计理念：
 * - 每个Hook都是独立的功能积木，可以自由组合
 * - 避免万能Hook，拒绝复杂设计
 * - 功能边界清晰，零配置开箱即用
 * 
 * 使用方式：
 * 1. 独立使用：import { useTheme } from '@/hooks'
 * 2. 组合使用：import { useUnifiedSystem } from '@/hooks'
 * 3. 全量引入：import * as hooks from '@/hooks'
 */

// 🎨 主题管理模块 (现已由ThemeContext提供)

// 📱 布局管理模块
export { 
  useLayout,
  type LayoutHook 
} from './useLayout';

// 🍎 iOS状态栏集成模块
export {
  useiOSStatusBar,
  type iOSStatusBarHook
} from './useiOSStatusBar';

// 🚀 iOS增强功能模块
export {
  useIOSEnhanced,
  type IOSEnhancedState,
  type IOSEnhancedActions
} from './useIOSEnhanced';

// 🎯 统一系统管理模块
export { 
  useUnifiedSystem,
  type UnifiedSystemHook,
  type SystemState 
} from './useUnifiedSystem';

// 📦 现有Hook模块（保持兼容）
export { useOfflineSync } from './useOfflineSync';
export { useCapacitorFeatures } from './useCapacitorFeatures';

// 🎨 容器宽度响应式布局Hook
export { useContainerWidth, useMuscleViewContainer } from './useContainerWidth'; 