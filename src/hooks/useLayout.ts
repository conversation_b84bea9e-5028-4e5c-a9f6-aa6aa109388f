import { useState, useCallback, useEffect } from 'react';
import { useSafeAreaReady } from './useSafeAreaReady';

/**
 * 布局Hook返回类型
 */
export interface LayoutHook {
  isReady: boolean;
  setupLayout: () => void;
  resetLayout: () => void;
  safeAreaReady: boolean; // 新增：Safe Area是否就绪
}

/**
 * 📱 简化的布局管理Hook - 与mobile-layout-unified.scss配合
 * 
 * 功能变更：
 * - ✅ 移除动态样式设置，避免与CSS冲突
 * - ✅ 保留接口兼容性，确保现有代码不受影响
 * - ✅ 只做基础DOM检查，布局完全由CSS处理
 * - ✅ 提供调试信息，便于开发时排查问题
 * 
 * 核心理念：
 * - CSS负责样式，JavaScript负责逻辑
 * - 减少运行时开销，提升性能
 * - 降低维护复杂度，避免样式冲突
 * 
 * @example
 * ```typescript
 * function Layout({ children }) {
 *   const { isReady } = useLayout();
 *   
 *   if (!isReady) return <div>Loading...</div>;
 *   
 *   return (
 *     <div className="layout mobile-layout">
 *       <header className="page-header">Header</header>
 *       <main className="main-content with-bottom-nav">
 *         <div className="page-content">{children}</div>
 *       </main>
 *       <footer className="bottom-navigation">Footer</footer>
 *     </div>
 *   );
 * }
 * ```
 */
export function useLayout(): LayoutHook {
  const [isReady, setIsReady] = useState(false);

  // 🔧 集成Safe Area监控
  const { isReady: safeAreaReady } = useSafeAreaReady();

  // 🔍 简化的布局检查逻辑 - 只做DOM元素检查
  const setupLayout = useCallback(() => {
    try {
      console.log('🔍 开始检查iOS布局元素...');
      
      // 检查关键布局元素是否存在
      const header = document.querySelector('.page-header');
      const footer = document.querySelector('.bottom-navigation');
      const content = document.querySelector('.page-content');
      const mainContent = document.querySelector('.main-content');
      const layout = document.querySelector('.layout');
      
      // 调试信息
      if (process.env.NODE_ENV === 'development') {
        console.log('📋 布局元素检查结果:', {
          header: !!header,
          footer: !!footer,
          content: !!content,
          mainContent: !!mainContent,
          layout: !!layout,
          isMobile: window.innerWidth < 768
        });
      }
      
      // 所有关键元素都存在时才标记为ready
      if (header && footer && content && mainContent && layout) {
        setIsReady(true);
        
        // 🔍 详细调试：Safe Area和底部导航状态 - 多方法检测
        try {
          // 多种方式获取Safe Area值
          const safeAreaValues = {
            // 方法1：CSS变量
            cssVarTop: getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top').trim(),
            cssVarBottom: getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-bottom').trim(),
            
            // 方法2：直接env()函数 (通常不工作)
            envTop: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)').trim(),
            envBottom: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)').trim(),
            
            // 方法3：通过测试元素获取
            testTop: (() => {
              const testEl = document.createElement('div');
              testEl.style.position = 'fixed';
              testEl.style.top = 'env(safe-area-inset-top)';
              testEl.style.visibility = 'hidden';
              testEl.style.pointerEvents = 'none';
              document.body.appendChild(testEl);
              const value = getComputedStyle(testEl).top;
              document.body.removeChild(testEl);
              return value;
            })(),
            
            testBottom: (() => {
              const testEl = document.createElement('div');
              testEl.style.position = 'fixed';
              testEl.style.bottom = 'env(safe-area-inset-bottom)';
              testEl.style.visibility = 'hidden';
              testEl.style.pointerEvents = 'none';
              document.body.appendChild(testEl);
              const value = getComputedStyle(testEl).bottom;
              document.body.removeChild(testEl);
              return value;
            })(),
            
            // 方法4：从元素实际样式中推断
            fromHeaderPadding: header ? getComputedStyle(header).paddingTop : '',
            fromFooterPadding: footer ? getComputedStyle(footer).paddingBottom : '',
          };
          
          // 优先选择最可靠的值
          const safeAreaTop = safeAreaValues.testTop !== '0px' ? safeAreaValues.testTop :
                              safeAreaValues.cssVarTop || safeAreaValues.envTop || 
                              safeAreaValues.fromHeaderPadding || '未获取到';
          const safeAreaBottom = safeAreaValues.testBottom !== '0px' ? safeAreaValues.testBottom :
                                  safeAreaValues.cssVarBottom || safeAreaValues.envBottom || 
                                  safeAreaValues.fromFooterPadding || '未获取到';
          
          // 底部导航详细状态
          const bottomNavStyle = window.getComputedStyle(footer);
          const bottomNavHeight = bottomNavStyle.height;
          const bottomNavBottom = bottomNavStyle.bottom;
          const bottomNavPaddingBottom = bottomNavStyle.paddingBottom;
          
          // Header详细状态  
          const headerStyle = window.getComputedStyle(header);
          const headerHeight = headerStyle.height;
          const headerPaddingTop = headerStyle.paddingTop;
          
          // 主内容区域状态
          const mainContentStyle = window.getComputedStyle(mainContent);
          const mainContentPaddingTop = mainContentStyle.paddingTop;
          const mainContentPaddingBottom = mainContentStyle.paddingBottom;
          
          console.log('🔍 === iOS布局详细调试信息 ===');
          console.log('📱 Safe Area多方法检测:');
          console.log(`   - CSS变量Top: ${safeAreaValues.cssVarTop || '无'}`);
          console.log(`   - CSS变量Bottom: ${safeAreaValues.cssVarBottom || '无'}`);
          console.log(`   - env()函数Top: ${safeAreaValues.envTop || '无'}`);
          console.log(`   - env()函数Bottom: ${safeAreaValues.envBottom || '无'}`);
          console.log(`   - 测试元素Top: ${safeAreaValues.testTop || '无'}`);
          console.log(`   - 测试元素Bottom: ${safeAreaValues.testBottom || '无'}`);
          console.log(`   - Header padding推断: ${safeAreaValues.fromHeaderPadding || '无'}`);
          console.log(`   - Footer padding推断: ${safeAreaValues.fromFooterPadding || '无'}`);
          console.log(`   - 🎯 最终推断Top: ${safeAreaTop}`);
          console.log(`   - 🎯 最终推断Bottom: ${safeAreaBottom}`);
          
          console.log('🔻 底部导航状态:');
          console.log(`   - Height: ${bottomNavHeight}`);
          console.log(`   - Bottom: ${bottomNavBottom}`);
          console.log(`   - PaddingBottom: ${bottomNavPaddingBottom}`);
          
          console.log('🔝 Header状态:');
          console.log(`   - Height: ${headerHeight}`);
          console.log(`   - PaddingTop: ${headerPaddingTop}`);
          
          console.log('🎯 主内容区域状态:');
          console.log(`   - PaddingTop: ${mainContentPaddingTop}`);
          console.log(`   - PaddingBottom: ${mainContentPaddingBottom}`);
          
          // 🔍 深度调试：检查底部导航的具体CSS规则
          try {
            // 获取实际应用的CSS规则
            const bottomNavComputedStyle = window.getComputedStyle(footer);
            
            console.log('🔍 === 底部导航CSS规则详细分析 ===');
            console.log(`   - 实际height计算: ${bottomNavComputedStyle.height}`);
            console.log(`   - 实际bottom值: ${bottomNavComputedStyle.bottom}`);
            console.log(`   - 实际paddingBottom: ${bottomNavComputedStyle.paddingBottom}`);
            console.log(`   - 实际marginBottom: ${bottomNavComputedStyle.marginBottom}`);
            console.log(`   - position类型: ${bottomNavComputedStyle.position}`);
            console.log(`   - z-index: ${bottomNavComputedStyle.zIndex}`);
            
            // 检查Safe Area的实时值
            const realTimeTop = getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)') || '未获取';
            const realTimeBottom = getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)') || '未获取';
            
            console.log('📱 === 实时Safe Area检测 ===');
            console.log(`   - 实时Top: ${realTimeTop}`);
            console.log(`   - 实时Bottom: ${realTimeBottom}`);
            
            // 尝试直接从CSS变量获取
            const cssVarTop = document.documentElement.style.getPropertyValue('--safe-area-inset-top') || '未设置';
            const cssVarBottom = document.documentElement.style.getPropertyValue('--safe-area-inset-bottom') || '未设置';
            
            console.log('🎨 === CSS变量检测 ===');
            console.log(`   - CSS变量Top: ${cssVarTop}`);
            console.log(`   - CSS变量Bottom: ${cssVarBottom}`);
            
            // 检查footer元素的class和应用的样式
            console.log('🏷️ === Footer元素分析 ===');
            console.log(`   - 元素类名: ${footer.className}`);
            console.log(`   - 元素ID: ${footer.id}`);
            console.log(`   - 元素标签: ${footer.tagName}`);
            
            // 尝试获取匹配的CSS规则
            const rules = [];
            try {
              for (let i = 0; i < document.styleSheets.length; i++) {
                const sheet = document.styleSheets[i];
                try {
                  const cssRules = sheet.cssRules || sheet.rules;
                  for (let j = 0; j < cssRules.length; j++) {
                    const rule = cssRules[j];
                    // 检查是否为CSSStyleRule类型
                    if (rule instanceof CSSStyleRule && rule.selectorText && footer.matches && footer.matches(rule.selectorText)) {
                      rules.push({
                        selector: rule.selectorText,
                        height: rule.style.height || '未设置',
                        paddingBottom: rule.style.paddingBottom || '未设置',
                        source: sheet.href || '内联样式'
                      });
                    }
                  }
                } catch (e) {
                  // 跨域或其他访问限制
                }
              }
            } catch (e) {
              console.log('   CSS规则检查受限');
            }
            
            console.log('📜 === 匹配的CSS规则 ===');
            if (rules.length > 0) {
              rules.forEach((rule, index) => {
                console.log(`   规则${index + 1}:`);
                console.log(`     - 选择器: ${rule.selector}`);
                console.log(`     - Height: ${rule.height}`);
                console.log(`     - PaddingBottom: ${rule.paddingBottom}`);
                console.log(`     - 来源: ${rule.source}`);
              });
            } else {
              console.log('   未找到匹配的CSS规则（可能由于访问限制）');
            }
            
          } catch (deepDebugError) {
            console.error('❌ 深度调试失败:', deepDebugError);
          }
          
          // 📋 样式文件状态检测 (修复版)
          console.log('📋 样式文件状态:');
          const allStylesheets = Array.from(document.styleSheets);
          let foundMobileLayout = false;
          let foundIosLayoutSimple = false;
          
          allStylesheets.forEach(sheet => {
            const href = sheet.href || '';
            try {
              const rules = Array.from(sheet.cssRules || []);
              const hasMobileLayoutRules = rules.some(rule => {
                if (rule.type === CSSRule.STYLE_RULE) {
                  const styleRule = rule as CSSStyleRule;
                  return styleRule.selectorText && (
                    styleRule.selectorText.includes('mobile-layout') ||
                    styleRule.selectorText.includes('page-header') ||
                    styleRule.selectorText.includes('bottom-navigation') ||
                    styleRule.selectorText.includes('supports (-webkit-touch-callout: none)')
                  );
                }
                return false;
              });
              if (hasMobileLayoutRules) foundMobileLayout = true;
            } catch (e) {
              // CORS限制，无法访问规则，但检查文件名
              if (href.includes('index-') && href.includes('.css')) {
                // 这很可能是编译后的文件，包含我们的mobile-layout样式
                foundMobileLayout = true;
              }
            }
            if (href.includes('ios-layout-fix-simple')) {
              foundIosLayoutSimple = true;
            }
          });
          
          console.log(`   - mobile-layout.scss: ${foundMobileLayout ? '✅已加载' : '❌未找到'}`);
          console.log(`   - ios-layout-fix-simple.scss: ${foundIosLayoutSimple ? '⚠️发现冲突' : '✅无冲突'}`);
          
          // 🔍 深度分析：底部导航Box Model
          console.log('🔍 === 底部导航Box Model深度分析 ===');
          const computedStyle = window.getComputedStyle(footer);
          const boxModel = {
            // 基础尺寸
            width: computedStyle.width,
            height: computedStyle.height,
            
            // 内边距
            paddingTop: computedStyle.paddingTop,
            paddingRight: computedStyle.paddingRight,
            paddingBottom: computedStyle.paddingBottom,
            paddingLeft: computedStyle.paddingLeft,
            
            // 边框
            borderTopWidth: computedStyle.borderTopWidth,
            borderRightWidth: computedStyle.borderRightWidth,
            borderBottomWidth: computedStyle.borderBottomWidth,
            borderLeftWidth: computedStyle.borderLeftWidth,
            
            // 外边距
            marginTop: computedStyle.marginTop,
            marginRight: computedStyle.marginRight,
            marginBottom: computedStyle.marginBottom,
            marginLeft: computedStyle.marginLeft,
            
            // 盒模型
            boxSizing: computedStyle.boxSizing,
            
                       // 实际客户区域
           clientHeight: (footer as HTMLElement).clientHeight,
           scrollHeight: (footer as HTMLElement).scrollHeight,
           offsetHeight: (footer as HTMLElement).offsetHeight,
          };
          
          console.log('📏 尺寸分析:');
          console.log(`   - 计算高度(getComputedStyle): ${boxModel.height}`);
          console.log(`   - 客户区高度(clientHeight): ${boxModel.clientHeight}px`);
          console.log(`   - 滚动高度(scrollHeight): ${boxModel.scrollHeight}px`);
          console.log(`   - 偏移高度(offsetHeight): ${boxModel.offsetHeight}px`);
          console.log(`   - 盒模型: ${boxModel.boxSizing}`);
          
          console.log('📦 间距分析:');
          console.log(`   - PaddingTop: ${boxModel.paddingTop}`);
          console.log(`   - PaddingBottom: ${boxModel.paddingBottom} (Safe Area)`);
          console.log(`   - BorderTop: ${boxModel.borderTopWidth}`);
          console.log(`   - BorderBottom: ${boxModel.borderBottomWidth}`);
          console.log(`   - MarginTop: ${boxModel.marginTop}`);
          console.log(`   - MarginBottom: ${boxModel.marginBottom}`);
          
          // 🧮 手动计算预期高度
          const paddingTopPx = parseFloat(boxModel.paddingTop) || 0;
          const paddingBottomPx = parseFloat(boxModel.paddingBottom) || 0;
          const borderTopPx = parseFloat(boxModel.borderTopWidth) || 0;
          const borderBottomPx = parseFloat(boxModel.borderBottomWidth) || 0;
          
          console.log('🧮 === 高度计算分析（Border-Box修正版）===');
          console.log(`   - CSS规则基础高度: 70px`);
          console.log(`   - 盒模型类型: ${boxModel.boxSizing}`);
          
          if (boxModel.boxSizing === 'border-box') {
            console.log('📦 Border-Box模式分析:');
            console.log(`   - 在border-box模式下，height包含padding和border`);
            console.log(`   - CSS height: 70px 应该等于总高度`);
            console.log(`   - 实际显示高度: ${boxModel.height}`);
            console.log(`   - 🎯 关键问题: 为什么实际是${boxModel.height}而不是70px？`);
            
            if (parseFloat(boxModel.height) !== 70) {
              console.log('⚠️ === 高度被覆盖！寻找覆盖规则 ===');
              
                             // 查找所有可能影响高度的CSS规则
               const allRules: Array<{
                 selector: string;
                 height: string;
                 minHeight: string;
                 maxHeight: string;
                 href: string;
               }> = [];
              Array.from(document.styleSheets).forEach(sheet => {
                try {
                  Array.from(sheet.cssRules || []).forEach(rule => {
                    if (rule.type === CSSRule.STYLE_RULE) {
                      const styleRule = rule as CSSStyleRule;
                      if (footer.matches(styleRule.selectorText)) {
                        const height = styleRule.style.height;
                        const minHeight = styleRule.style.minHeight;
                        const maxHeight = styleRule.style.maxHeight;
                                                 if (height || minHeight || maxHeight) {
                           allRules.push({
                             selector: styleRule.selectorText,
                             height: height || '',
                             minHeight: minHeight || '',
                             maxHeight: maxHeight || '',
                             href: sheet.href || '内联样式'
                           });
                        }
                      }
                    }
                  });
                } catch (e) {
                  // CORS限制
                }
              });
              
              console.log('🔍 === 所有影响高度的CSS规则 ===');
              allRules.forEach((rule, index) => {
                console.log(`   规则${index + 1}:`);
                console.log(`     - 选择器: ${rule.selector}`);
                if (rule.height) console.log(`     - Height: ${rule.height}`);
                if (rule.minHeight) console.log(`     - MinHeight: ${rule.minHeight}`);
                if (rule.maxHeight) console.log(`     - MaxHeight: ${rule.maxHeight}`);
                console.log(`     - 来源: ${rule.href}`);
              });
              
              // 计算实际的内容高度
              const contentHeight = parseFloat(boxModel.height) - paddingTopPx - paddingBottomPx - borderTopPx - borderBottomPx;
              console.log(`🧮 实际内容区域计算:`);
              console.log(`   - 总高度: ${boxModel.height}`);
              console.log(`   - 减去padding: ${paddingTopPx + paddingBottomPx}px`);
              console.log(`   - 减去border: ${borderTopPx + borderBottomPx}px`);
              console.log(`   - 内容区域高度: ${contentHeight}px`);
            }
          } else {
            // content-box模式的计算（应该不会执行，但保留逻辑）
            const expectedHeight = 70 + paddingTopPx + paddingBottomPx + borderTopPx + borderBottomPx;
            console.log(`   - 预期总高度: 70 + ${paddingTopPx} + ${paddingBottomPx} + ${borderTopPx} + ${borderBottomPx} = ${expectedHeight}px`);
            console.log(`   - 实际显示高度: ${boxModel.height}`);
            console.log(`   - 高度差异: ${parseFloat(boxModel.height) - expectedHeight}px`);
          }
          
          // 🔍 检查是否有其他影响高度的CSS属性
          console.log('🔍 === 其他影响高度的CSS属性 ===');
          const heightRelatedProps = {
            minHeight: computedStyle.minHeight,
            maxHeight: computedStyle.maxHeight,
            lineHeight: computedStyle.lineHeight,
            fontSize: computedStyle.fontSize,
            display: computedStyle.display,
            flexShrink: computedStyle.flexShrink,
            flexGrow: computedStyle.flexGrow,
            flexBasis: computedStyle.flexBasis,
          };
          
          Object.entries(heightRelatedProps).forEach(([prop, value]) => {
            if (value && value !== 'normal' && value !== 'auto' && value !== '0') {
              console.log(`   - ${prop}: ${value}`);
            }
          });
          
          // 检查是否为初始化状态
          const isInitialLoad = !sessionStorage.getItem('layout_initialized');
          const currentTime = new Date().toISOString();
          const currentUrl = window.location.href;
          
          if (isInitialLoad) {
            sessionStorage.setItem('layout_initialized', 'true');
            sessionStorage.setItem('last_debug_time', currentTime);
            console.log('🚀 === 初始化状态检测 ===');
            console.log('   这是应用初次加载，记录基准状态');
            console.log(`   - 时间: ${currentTime}`);
            console.log(`   - URL: ${currentUrl}`);
            
            // 🔍 初始化专用：高度变化实时监控
            console.log('📊 === 初始化高度变化监控启动 ===');
            let monitorCount = 0;
            const heightMonitor = setInterval(() => {
              monitorCount++;
              const currentFooter = document.querySelector('.bottom-navigation') as HTMLElement;
              
              if (currentFooter) {
                const currentStyle = getComputedStyle(currentFooter);
                const currentHeight = currentStyle.height;
                const currentPadding = currentStyle.paddingBottom;
                
                // 测试env()值的实时变化
                const testEnvBottom = (() => {
                  const testEl = document.createElement('div');
                  testEl.style.cssText = 'position:fixed;bottom:env(safe-area-inset-bottom);visibility:hidden;';
                  document.body.appendChild(testEl);
                  const value = getComputedStyle(testEl).bottom;
                  document.body.removeChild(testEl);
                  return value;
                })();
                
                // 测试calc()的实际计算值
                const testCalc = (() => {
                  const testEl = document.createElement('div');
                  testEl.style.cssText = 'height:calc(60px + env(safe-area-inset-bottom));visibility:hidden;';
                  document.body.appendChild(testEl);
                  const value = getComputedStyle(testEl).height;
                  document.body.removeChild(testEl);
                  return value;
                })();
                
                console.log(`📊 [${monitorCount}] ${Date.now()}ms - 高度监控:`);
                console.log(`   - 实际高度: ${currentHeight}`);
                console.log(`   - PaddingBottom: ${currentPadding}`);
                console.log(`   - env(safe-area-inset-bottom): ${testEnvBottom}`);
                console.log(`   - calc(60px + env(...)): ${testCalc}`);
                
                // 检查是否有异常值
                const heightPx = parseFloat(currentHeight);
                const paddingPx = parseFloat(currentPadding);
                const calcPx = parseFloat(testCalc);
                
                if (heightPx > 120 || paddingPx > 50 || calcPx > 120) {
                  console.log('🚨 === 发现异常高度值！ ===');
                  console.log(`   - 高度异常: ${heightPx}px > 120px`);
                  console.log(`   - padding异常: ${paddingPx}px > 50px`);
                  console.log(`   - calc异常: ${calcPx}px > 120px`);
                  console.log('   - 可能原因: env()函数返回异常值');
                }
                
                // 10次检测后停止
                if (monitorCount >= 10) {
                  clearInterval(heightMonitor);
                  console.log('📊 === 初始化高度监控结束 ===');
                  console.log(`   - 最终稳定高度: ${currentHeight}`);
                  console.log(`   - 最终稳定padding: ${currentPadding}`);
                }
              } else {
                console.log(`📊 [${monitorCount}] 底部导航元素尚未挂载`);
                if (monitorCount >= 15) {
                  clearInterval(heightMonitor);
                  console.log('⚠️ === 初始化监控超时 ===');
                }
              }
            }, 100); // 每100ms检测一次
            
          } else {
            const lastDebugTime = sessionStorage.getItem('last_debug_time');
            sessionStorage.setItem('last_debug_time', currentTime);
            console.log('🔄 === 页面切换状态检测 ===');
            console.log('   这是页面切换后的状态');
            console.log(`   - 当前时间: ${currentTime}`);
            console.log(`   - 上次调试: ${lastDebugTime}`);
            console.log(`   - 当前URL: ${currentUrl}`);
            console.log(`   - 🎯 页面切换调试确认：如果你看到这条消息，说明调试代码正常运行`);
          }
          
          // 🔍 添加页面切换专用的简化调试
          if (!isInitialLoad) {
            console.log('📱 === 页面切换简化状态检查 ===');
            console.log(`   - 底部导航高度: ${bottomNavHeight}`);
            console.log(`   - 底部导航padding: ${bottomNavPaddingBottom}`);
            console.log(`   - Safe Area Bottom: ${safeAreaBottom}`);
            console.log(`   - Header高度: ${headerHeight}`);
            console.log(`   - Header padding: ${headerPaddingTop}`);
            console.log(`   - Safe Area Top: ${safeAreaTop}`);
            
            // 强制刷新调试，避免hang影响
            setTimeout(() => {
              console.log('⏰ === 页面切换2秒后强制状态确认 ===');
              console.log(`   - 当前时间: ${new Date().toISOString()}`);
              console.log(`   - 底部导航元素存在: ${!!document.querySelector('.bottom-navigation')}`);
              console.log(`   - Header元素存在: ${!!document.querySelector('.page-header')}`);
              console.log(`   - 这条消息确认页面切换调试没有被hang阻塞`);
            }, 2000);
          }
          
          // 延迟检测，看状态是否会变化
          setTimeout(() => {
            const delayedBottomNavStyle = window.getComputedStyle(footer);
            const delayedHeight = delayedBottomNavStyle.height;
            const delayedBottom = delayedBottomNavStyle.bottom;
            
            if (delayedHeight !== bottomNavHeight || delayedBottom !== bottomNavBottom) {
              console.log('⚠️ === 延迟检测发现变化 ===');
              console.log(`   初始高度: ${bottomNavHeight} -> 延迟高度: ${delayedHeight}`);
              console.log(`   初始底部: ${bottomNavBottom} -> 延迟底部: ${delayedBottom}`);
            } else {
              console.log('✅ === 延迟检测无变化 ===');
              console.log('   底部导航状态稳定');
            }
          }, 1000);
          
          // 🔍 Safe Area值持续监控 - 增强版多方法检测
          let safeAreaCheckCount = 0;
          const safeAreaMonitor = setInterval(() => {
            safeAreaCheckCount++;
            
            // 使用测试元素方法检测
            const testElTop = document.createElement('div');
            testElTop.style.cssText = 'position:fixed;top:env(safe-area-inset-top);visibility:hidden;pointer-events:none;';
            document.body.appendChild(testElTop);
            const currentTop = getComputedStyle(testElTop).top;
            document.body.removeChild(testElTop);
            
            const testElBottom = document.createElement('div');
            testElBottom.style.cssText = 'position:fixed;bottom:env(safe-area-inset-bottom);visibility:hidden;pointer-events:none;';
            document.body.appendChild(testElBottom);
            const currentBottom = getComputedStyle(testElBottom).bottom;
            document.body.removeChild(testElBottom);
            
            // 检查是否有有效值（不是0px或auto）
            const hasValidTop = currentTop && currentTop !== '0px' && currentTop !== 'auto';
            const hasValidBottom = currentBottom && currentBottom !== '0px' && currentBottom !== 'auto';
            
            if (hasValidTop || hasValidBottom || safeAreaCheckCount >= 10) {
              clearInterval(safeAreaMonitor);
              
              if (hasValidTop || hasValidBottom) {
                console.log(`🎯 === Safe Area值在${safeAreaCheckCount * 500}ms后可用 ===`);
                console.log(`   - Top变为: ${currentTop}`);
                console.log(`   - Bottom变为: ${currentBottom}`);
                
                // 重新检查底部导航状态
                const updatedBottomNavStyle = window.getComputedStyle(footer);
                console.log('🔄 === Safe Area更新后的底部导航状态 ===');
                console.log(`   - 更新后Height: ${updatedBottomNavStyle.height}`);
                console.log(`   - 更新后PaddingBottom: ${updatedBottomNavStyle.paddingBottom}`);
                
                // 分析为什么初始化时Safe Area不可用
                console.log('💡 === Safe Area延迟可用分析 ===');
                console.log(`   - 检测次数: ${safeAreaCheckCount}`);
                console.log(`   - 延迟时间: ${safeAreaCheckCount * 500}ms`);
                console.log(`   - 可能原因: Capacitor初始化延迟或iOS WebView安全限制`);
              } else {
                console.log(`⚠️ === Safe Area值在${safeAreaCheckCount * 500}ms后仍未可用 ===`);
                console.log('💡 可能原因: 1) 非iOS设备 2) 环境变量未正确注入 3) WebView配置问题');
              }
            }
          }, 500);
          
        } catch (debugError) {
          console.error('❌ 调试信息收集失败:', debugError);
        }
        
        console.log('✅ iOS布局元素检查完成，样式由mobile-layout.scss处理');
      } else {
                console.warn('⚠️ 部分布局元素未找到，等待DOM更新...');
        console.log('🔍 缺失元素详情:');
        console.log(`   - Header: ${header ? '✅' : '❌'}`);
        console.log(`   - Footer: ${footer ? '✅' : '❌'}`);
        console.log(`   - Content: ${content ? '✅' : '❌'}`);
        console.log(`   - MainContent: ${mainContent ? '✅' : '❌'}`);
        console.log(`   - Layout: ${layout ? '✅' : '❌'}`);
        setIsReady(false);
      }
    } catch (error) {
      console.error('❌ 布局检查失败:', error);
      setIsReady(false);
    }
  }, []);

  // 🧹 简化的重置逻辑 - 只重置状态
  const resetLayout = useCallback(() => {
    try {
      setIsReady(false);
      console.log('🧹 iOS布局Hook已重置，样式保持由CSS控制');
    } catch (error) {
      console.error('❌ 布局重置失败:', error);
    }
  }, []);

  // 📋 组件挂载时检查布局元素
  useEffect(() => {
    // 初始检查
    setupLayout();
    
    // 监听DOM变化，确保动态内容加载后能正确检查
    const observer = new MutationObserver((mutations) => {
      let shouldRecheck = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.classList?.contains('page-header') ||
                  element.classList?.contains('bottom-navigation') ||
                  element.classList?.contains('page-content') ||
                  element.classList?.contains('main-content') ||
                  element.classList?.contains('layout')) {
                shouldRecheck = true;
              }
            }
          });
        }
      });
      
      if (shouldRecheck) {
        console.log('🔄 检测到布局元素变化，重新检查...');
        setTimeout(setupLayout, 50);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 🧹 组件卸载时清理
    return () => {
      observer.disconnect();
      resetLayout();
    };
  }, [setupLayout, resetLayout]);

  // 🔄 监听窗口大小变化，重新检查布局
  useEffect(() => {
    if (!isReady) return;

    const handleResize = () => {
      console.log('📱 窗口大小变化，重新检查布局');
      setTimeout(setupLayout, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, [isReady, setupLayout]);

  return {
    isReady,
    setupLayout,
    resetLayout,
    safeAreaReady
  };
} 