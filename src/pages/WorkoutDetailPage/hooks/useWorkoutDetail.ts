/**
 * useWorkoutDetail Hook
 * 
 * @fileoverview WorkoutDetail页面的核心数据获取和状态管理Hook
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { UIWorkoutDetail } from '../../../models/ui/workout';
import { WorkoutDetailTransformer } from '../../../models/transformers/workout/WorkoutDetailTransformer';
import { communityService } from '../../../services/communityService';
import { workoutService } from '../../../services/workout';
import { DataSource, SocialAction } from '../types';

export interface UseWorkoutDetailResult {
  workoutDetail: UIWorkoutDetail | null;
  loading: boolean;
  error: string | null;
  
  // 交互状态
  expandedExercises: string[];
  
  // 交互方法
  toggleExerciseExpansion: (exerciseId: string) => void;
  handleSocialAction: (action: SocialAction) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * WorkoutDetail 数据获取和状态管理 Hook
 * 
 * @param dataSource 数据源配置
 * @param initialData 初始数据（可选，用于避免重复请求）
 * @returns Hook 结果对象
 */
export const useWorkoutDetail = (
  dataSource: DataSource,
  initialData?: any
): UseWorkoutDetailResult => {
  const [workoutDetail, setWorkoutDetail] = useState<UIWorkoutDetail | null>(null);
  const [loading, setLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [expandedExercises, setExpandedExercises] = useState<string[]>([]);

  // 数据获取函数
  const fetchWorkoutDetail = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      let result: UIWorkoutDetail;
      
      if (dataSource.type === 'feed') {
        // Feed 场景：从社区服务获取帖子详情
        const feedPost = await communityService.getPostDetail(dataSource.id);
        result = WorkoutDetailTransformer.transformPostDetailToUIModel(feedPost);
      } else {
        // Profile 场景：从训练服务获取训练详情
        const response = await workoutService.getWorkoutDetail(dataSource.id);
        if (!response.success || !response.data) {
          throw new Error(response.error?.message || '获取训练详情失败');
        }
        result = WorkoutDetailTransformer.transformWorkoutResponseToUIModel(response.data);
      }
      
      setWorkoutDetail(result);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取训练详情失败:', err);
    } finally {
      setLoading(false);
    }
  }, [dataSource]);

  // 初始化数据获取
  useEffect(() => {
    if (initialData) {
      // 使用初始数据，避免重复网络请求
      try {
        const result = dataSource.type === 'feed'
          ? WorkoutDetailTransformer.transformPostDetailToUIModel(initialData)
          : WorkoutDetailTransformer.transformWorkoutResponseToUIModel(initialData);
        
        setWorkoutDetail(result);
        setLoading(false);
      } catch (err) {
        console.error('初始数据转换失败:', err);
        // 降级到网络请求
        fetchWorkoutDetail();
      }
    } else {
      fetchWorkoutDetail();
    }
  }, [dataSource, initialData, fetchWorkoutDetail]);

  // 动作展开/折叠
  const toggleExerciseExpansion = useCallback((exerciseId: string) => {
    setExpandedExercises(prev => 
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    );
  }, []);

  // 社交操作处理
  const handleSocialAction = useCallback(async (action: SocialAction) => {
    if (!workoutDetail?.socialInfo) {
      console.warn('当前页面不支持社交操作');
      return;
    }
    
    try {
      switch (action.type) {
        case 'like':
          // 使用社区服务的点赞功能
          await communityService.togglePostLike(workoutDetail.socialInfo.post.id);
          
          // 乐观更新UI
          setWorkoutDetail(prev => prev ? {
            ...prev,
            socialInfo: {
              ...prev.socialInfo!,
              stats: {
                ...prev.socialInfo!.stats,
                isLiked: !prev.socialInfo!.stats.isLiked,
                likes: prev.socialInfo!.stats.isLiked 
                  ? prev.socialInfo!.stats.likes - 1
                  : prev.socialInfo!.stats.likes + 1
              }
            }
          } : null);
          break;
          
        case 'follow':
          // TODO: 实现关注逻辑
          console.log('关注功能待实现');
          break;
          
        case 'share':
          // TODO: 实现分享逻辑
          console.log('分享功能待实现');
          break;
          
        case 'comment':
          // TODO: 实现评论逻辑
          console.log('评论功能待实现');
          break;
          
        default:
          console.warn('未知的社交操作类型:', action.type);
      }
    } catch (err) {
      console.error('社交操作失败:', err);
      // TODO: 显示错误提示给用户
      // 如果是乐观更新，这里应该回滚状态
    }
  }, [workoutDetail]);

  // 刷新数据
  const refreshData = useCallback(async () => {
    await fetchWorkoutDetail();
  }, [fetchWorkoutDetail]);

  // 返回 Hook 结果
  return useMemo(() => ({
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  }), [
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  ]);
};