/**
 * TrainingExerciseCard 训练动作卡片组件
 * 
 * @fileoverview 展示单个训练动作的详细记录，支持展开/折叠查看组记录
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import type { UITrainingExercise, UISetRecord } from '../../../../models/ui/workout';
import './TrainingExerciseCard.scss';

/**
 * TrainingExerciseCard 组件属性
 */
export interface TrainingExerciseCardProps {
  /** 训练动作数据 */
  exercise: UITrainingExercise;
  /** 是否展开显示详情 */
  isExpanded: boolean;
  /** 切换展开状态的回调 */
  onToggleExpansion: () => void;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 训练动作卡片组件
 * 支持iOS原生触摸反馈和流畅的展开/折叠动画
 */
export const TrainingExerciseCard: React.FC<TrainingExerciseCardProps> = ({
  exercise,
  isExpanded,
  onToggleExpansion,
  className = ''
}) => {
  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 处理点击切换展开状态
  const handleToggle = useCallback(() => {
    provideiOSTouchFeedback();
    onToggleExpansion();
  }, [onToggleExpansion, provideiOSTouchFeedback]);

  // 计算完成进度百分比
  const completionPercentage = exercise.sets > 0 
    ? Math.round((exercise.completedSets / exercise.sets) * 100)
    : 0;

  // 格式化组记录状态显示
  const formatSetStatus = (record: UISetRecord) => {
    if (record.completed) {
      return '✓';
    }
    return '○';
  };

  // 获取组记录类型样式
  const getSetTypeClass = (record: UISetRecord) => {
    switch (record.setType) {
      case 'warmup':
        return 'warmup';
      case 'dropset':
        return 'dropset';
      case 'failure':
        return 'failure';
      default:
        return 'normal';
    }
  };

  return (
    <div className={`training-exercise-card ${isExpanded ? 'expanded' : 'collapsed'} ${className}`}>
      {/* 动作头部信息 */}
      <div className="exercise-header" onClick={handleToggle}>
        <div className="exercise-main-info">
          <h3 className="exercise-name">{exercise.name}</h3>
          <div className="exercise-summary">
            <span className="sets-reps">
              {exercise.completedSets}/{exercise.sets}组 × {exercise.targetReps}次
            </span>
            {exercise.totalVolume > 0 && (
              <span className="volume">
                {exercise.totalVolume}kg
              </span>
            )}
          </div>
        </div>
        
        <div className="exercise-actions">
          {/* 完成状态标识 */}
          <div className="completion-badge">
            {exercise.completedSets === exercise.sets ? (
              <div className="completion-icon completed">
                ✓
              </div>
            ) : (
              <div className="progress-ring">
                <span>{completionPercentage}%</span>
              </div>
            )}
          </div>
          
          {/* 展开/折叠按钮 */}
          <button 
            className="expand-button" 
            aria-label={isExpanded ? '收起动作详情' : '展开动作详情'}
            type="button"
          >
            <span className={`chevron ${isExpanded ? 'rotated' : ''}`}>
              ▼
            </span>
          </button>
        </div>
      </div>
      
      {/* 动作详情区域 */}
      <div className={`exercise-details ${isExpanded ? 'expanded' : 'collapsed'}`}>
        {/* 组记录列表 */}
        <div className="set-records-container">
          <div className="set-records-header">
            <span>组</span>
            <span>重量</span>
            <span>次数</span>
            <span>状态</span>
          </div>
          
          <div className="set-records-list">
            {exercise.setRecords.map((record: UISetRecord) => (
              <div 
                key={record.id} 
                className={`set-record ${getSetTypeClass(record)} ${record.completed ? 'completed' : 'pending'}`}
              >
                <span className="set-number">{record.setNumber}</span>
                <span className="weight">{record.weight}kg</span>
                <span className="reps">{record.reps}</span>
                <span className="status">
                  {formatSetStatus(record)}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* 动作统计信息 */}
        <div className="exercise-stats">
          <div className="stat-item">
            <span className="label">平均重量</span>
            <span className="value">{exercise.averageWeight.toFixed(1)}kg</span>
          </div>
          <div className="stat-item">
            <span className="label">休息时间</span>
            <span className="value">{exercise.restSeconds}s</span>
          </div>
          <div className="stat-item">
            <span className="label">总重量</span>
            <span className="value">{exercise.totalVolume}kg</span>
          </div>
        </div>
        
        {/* 动作备注 */}
        {exercise.notes && (
          <div className="exercise-notes">
            <h4>备注</h4>
            <p>{exercise.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainingExerciseCard;