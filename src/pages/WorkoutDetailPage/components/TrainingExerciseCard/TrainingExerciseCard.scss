/**
 * TrainingExerciseCard 样式
 * 
 * @fileoverview 训练动作卡片的样式定义，符合iOS HIG标准
 * <AUTHOR> Team
 * @since 1.0.0
 */

// iOS触摸目标样式混入
@mixin ios-touch-target {
  min-width: var(--ios-touch-target); // 44px
  min-height: var(--ios-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  cursor: pointer;
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:active {
    transform: scale(0.98);
  }
}

.training-exercise-card {
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: var(--space-4);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // GPU加速优化
  transform: translateZ(0);
  
  &.expanded {
    border-color: var(--accent-200);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .exercise-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    // iOS触摸反馈
    &:active {
      background: var(--bg-hover);
      transform: scale(0.995);
    }
    
    .exercise-main-info {
      flex: 1;
      min-width: 0; // 防止flex item溢出
      
      .exercise-name {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-1);
        
        // 文本截断
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .exercise-summary {
        display: flex;
        gap: var(--space-3);
        flex-wrap: wrap;
        
        .sets-reps {
          font-size: var(--text-sm);
          color: var(--text-secondary);
          font-weight: var(--font-medium);
        }
        
        .volume {
          font-size: var(--text-sm);
          color: var(--accent-500);
          font-weight: var(--font-semibold);
          background: var(--accent-50);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
    
    .exercise-actions {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      flex-shrink: 0;
      
      .completion-badge {
        .completion-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--text-base);
          font-weight: var(--font-bold);
          
          &.completed {
            background: var(--success-500);
            color: white;
          }
        }
        
        .progress-ring {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid var(--accent-200);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--text-xs);
          font-weight: var(--font-semibold);
          color: var(--accent-500);
          background: var(--accent-50);
        }
      }
      
      .expand-button {
        @include ios-touch-target;
        width: 32px;
        height: 32px;
        background: none;
        border: none;
        color: var(--text-secondary);
        padding: 0;
        
        .chevron {
          font-size: 12px;
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          
          &.rotated {
            transform: rotate(180deg);
          }
        }
        
        &:hover {
          color: var(--text-primary);
        }
      }
    }
  }
  
  .exercise-details {
    border-top: 1px solid var(--border-color);
    overflow: hidden;
    
    // GPU加速的展开/折叠动画
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
    
    &.collapsed {
      max-height: 0;
    }
    
    &.expanded {
      max-height: 600px; // 足够的高度容纳内容
    }
    
    .set-records-container {
      padding: var(--space-4);
      
      .set-records-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: var(--space-2);
        padding-bottom: var(--space-2);
        border-bottom: 1px solid var(--border-color);
        margin-bottom: var(--space-3);
        
        span {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--text-secondary);
          text-align: center;
        }
      }
      
      .set-records-list {
        .set-record {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr 1fr;
          gap: var(--space-2);
          padding: var(--space-2) var(--space-1);
          border-bottom: 1px solid var(--border-light);
          border-radius: 6px;
          transition: background-color 0.2s ease;
          
          &:last-child {
            border-bottom: none;
          }
          
          // 组类型样式
          &.warmup {
            background: var(--warning-50);
            border-left: 3px solid var(--warning-400);
          }
          
          &.dropset {
            background: var(--info-50);
            border-left: 3px solid var(--info-400);
          }
          
          &.failure {
            background: var(--error-50);
            border-left: 3px solid var(--error-400);
          }
          
          &.completed {
            background: var(--success-50);
            border-left: 3px solid var(--success-400);
          }
          
          &.pending {
            background: var(--bg-secondary);
          }
          
          span {
            font-size: var(--text-sm);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &.weight, &.reps {
              font-weight: var(--font-semibold);
              color: var(--text-primary);
            }
            
            &.set-number {
              color: var(--text-secondary);
              font-weight: var(--font-medium);
            }
            
            &.status {
              font-size: var(--text-base);
              font-weight: var(--font-bold);
            }
          }
        }
      }
    }
    
    .exercise-stats {
      display: flex;
      justify-content: space-around;
      padding: var(--space-4);
      background: var(--bg-secondary);
      border-top: 1px solid var(--border-color);
      
      .stat-item {
        text-align: center;
        
        .label {
          display: block;
          font-size: var(--text-xs);
          color: var(--text-secondary);
          margin-bottom: var(--space-1);
          font-weight: var(--font-medium);
        }
        
        .value {
          display: block;
          font-size: var(--text-base);
          font-weight: var(--font-bold);
          color: var(--text-primary);
        }
      }
    }
    
    .exercise-notes {
      padding: var(--space-4);
      border-top: 1px solid var(--border-color);
      background: var(--bg-tertiary);
      
      h4 {
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2);
      }
      
      p {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

// iOS专用媒体查询优化
@media only screen and (max-width: 768px) {
  .training-exercise-card {
    border-radius: 8px;
    
    .exercise-header {
      padding: var(--space-3);
    }
    
    .exercise-details {
      .set-records-container {
        padding: var(--space-3);
      }
      
      .exercise-stats {
        padding: var(--space-3);
      }
      
      .exercise-notes {
        padding: var(--space-3);
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .training-exercise-card {
    .exercise-details {
      .set-record {
        &.warmup {
          background: rgba(251, 191, 36, 0.1);
        }
        
        &.dropset {
          background: rgba(59, 130, 246, 0.1);
        }
        
        &.failure {
          background: rgba(239, 68, 68, 0.1);
        }
        
        &.completed {
          background: rgba(34, 197, 94, 0.1);
        }
      }
    }
  }
}