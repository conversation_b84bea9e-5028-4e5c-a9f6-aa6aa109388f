/**
 * SocialSection 社交信息区域组件
 * 
 * @fileoverview Feed场景下的社交信息展示，包含用户信息、帖子内容和社交统计
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import type { UIWorkoutDetail } from '../../../../models/ui/workout';
import type { SocialAction } from '../../types';
import { PostUserSection, PostActions } from '../../../../components/common';
import type { PostUser, PostStats } from '../../../../components/common';
import './SocialSection.scss';

/**
 * SocialSection 组件属性
 */
export interface SocialSectionProps {
  /** 社交信息数据 */
  socialInfo: UIWorkoutDetail['socialInfo'];
  /** 社交操作回调 */
  onSocialAction: (action: SocialAction) => Promise<void>;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 社交信息区域组件
 * 仅在Feed场景下显示，包含用户头像、帖子内容、社交统计和操作按钮
 */
export const SocialSection: React.FC<SocialSectionProps> = ({
  socialInfo,
  onSocialAction,
  className = ''
}) => {
  // 如果没有社交信息，不渲染
  if (!socialInfo) {
    return null;
  }

  // 转换用户数据格式
  const postUser: PostUser = {
    id: socialInfo.user.id,
    name: socialInfo.user.name,
    avatar: socialInfo.user.avatar,
    isVerified: socialInfo.user.isVerified,
    isFollowing: socialInfo.user.isFollowing
  };

  // 转换统计数据格式
  const postStats: PostStats = {
    likes: socialInfo.stats.likes,
    comments: socialInfo.stats.comments,
    views: socialInfo.stats.views,
    isLiked: socialInfo.stats.isLiked
  };

  // 处理关注操作
  const handleFollow = useCallback(async (userId: string, isCurrentlyFollowing: boolean) => {
    await onSocialAction({ type: 'follow' });
  }, [onSocialAction]);

  // 处理点赞操作
  const handleLike = useCallback(async () => {
    await onSocialAction({ type: 'like' });
  }, [onSocialAction]);

  // 处理评论操作
  const handleComment = useCallback(async () => {
    await onSocialAction({ type: 'comment' });
  }, [onSocialAction]);

  // 处理分享操作
  const handleShare = useCallback(async () => {
    await onSocialAction({ type: 'share' });
  }, [onSocialAction]);

  return (
    <section className={`social-section ${className}`}>
      {/* 用户信息 - 使用统一的PostUserSection组件 */}
      <div className="post-header">
        <PostUserSection
          user={postUser}
          timestamp={socialInfo.post.timestamp}
          showFollowButton={true}
          onFollow={handleFollow}
          className="social-user-section"
        />
      </div>

      {/* 帖子内容 */}
      {socialInfo.post.content && (
        <div className="post-content">
          <p>{socialInfo.post.content}</p>
        </div>
      )}

      {/* 帖子图片（如果有） */}
      {socialInfo.post.images && socialInfo.post.images.length > 0 && (
        <div className="post-images">
          {socialInfo.post.images.map((image: string, index: number) => (
            <img
              key={index}
              src={image}
              alt={`帖子图片 ${index + 1}`}
              className="post-image"
              loading="lazy"
            />
          ))}
        </div>
      )}

      {/* 社交操作 - 使用统一的PostActions组件 */}
      <PostActions
        stats={postStats}
        onLike={handleLike}
        onComment={handleComment}
        onShare={handleShare}
        className="social-actions"
      />
    </section>
  );
};

export default SocialSection;