/**
 * SocialSection 社交信息区域组件
 * 
 * @fileoverview Feed场景下的社交信息展示，包含用户信息、帖子内容和社交统计
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import type { UIWorkoutDetail } from '../../../../models/ui/workout';
import type { SocialAction } from '../../types';
import './SocialSection.scss';

/**
 * SocialSection 组件属性
 */
export interface SocialSectionProps {
  /** 社交信息数据 */
  socialInfo: UIWorkoutDetail['socialInfo'];
  /** 社交操作回调 */
  onSocialAction: (action: SocialAction) => Promise<void>;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 社交信息区域组件
 * 仅在Feed场景下显示，包含用户头像、帖子内容、社交统计和操作按钮
 */
export const SocialSection: React.FC<SocialSectionProps> = ({
  socialInfo,
  onSocialAction,
  className = ''
}) => {
  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 如果没有社交信息，不渲染
  if (!socialInfo) {
    return null;
  }

  // 处理点赞操作
  const handleLike = useCallback(async () => {
    provideiOSTouchFeedback();
    await onSocialAction({ type: 'like' });
  }, [onSocialAction, provideiOSTouchFeedback]);

  // 处理关注操作
  const handleFollow = useCallback(async () => {
    provideiOSTouchFeedback();
    await onSocialAction({ type: 'follow' });
  }, [onSocialAction, provideiOSTouchFeedback]);

  // 处理分享操作
  const handleShare = useCallback(async () => {
    provideiOSTouchFeedback();
    await onSocialAction({ type: 'share' });
  }, [onSocialAction, provideiOSTouchFeedback]);

  // 处理评论操作
  const handleComment = useCallback(async () => {
    provideiOSTouchFeedback();
    await onSocialAction({ type: 'comment' });
  }, [onSocialAction, provideiOSTouchFeedback]);

  // 格式化时间显示
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return '刚刚';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    }
  };

  // 格式化数字显示（k, w）
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}w`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  return (
    <section className={`social-section ${className}`}>
      {/* 用户信息 */}
      <div className="user-info">
        <div className="user-avatar-container">
          <img 
            src={socialInfo.user.avatar} 
            alt={`${socialInfo.user.name}的头像`}
            className="user-avatar"
            loading="lazy"
          />
          {socialInfo.user.isVerified && (
            <div className="verified-badge">
              ✓
            </div>
          )}
        </div>
        
        <div className="user-details">
          <div className="user-name-row">
            <h3 className="user-name">{socialInfo.user.name}</h3>
            <span className="post-timestamp">
              {formatTimestamp(socialInfo.post.timestamp)}
            </span>
          </div>
          
          {!socialInfo.user.isFollowing && (
            <button 
              className="follow-button"
              onClick={handleFollow}
              type="button"
            >
              关注
            </button>
          )}
        </div>
      </div>

      {/* 帖子内容 */}
      {socialInfo.post.content && (
        <div className="post-content">
          <p>{socialInfo.post.content}</p>
        </div>
      )}

      {/* 帖子图片（如果有） */}
      {socialInfo.post.images && socialInfo.post.images.length > 0 && (
        <div className="post-images">
          {socialInfo.post.images.map((image: string, index: number) => (
            <img
              key={index}
              src={image}
              alt={`帖子图片 ${index + 1}`}
              className="post-image"
              loading="lazy"
            />
          ))}
        </div>
      )}

      {/* 社交统计和操作 */}
      <div className="social-stats-actions">
        {/* 统计信息 */}
        <div className="social-stats">
          <span className="stat-item">
            <span className="stat-number">{formatNumber(socialInfo.stats.likes)}</span>
            <span className="stat-label">赞</span>
          </span>
          <span className="stat-item">
            <span className="stat-number">{formatNumber(socialInfo.stats.comments)}</span>
            <span className="stat-label">评论</span>
          </span>
          <span className="stat-item">
            <span className="stat-number">{formatNumber(socialInfo.stats.views)}</span>
            <span className="stat-label">浏览</span>
          </span>
        </div>

        {/* 操作按钮 */}
        <div className="social-actions">
          <button 
            className={`action-button like-button ${socialInfo.stats.isLiked ? 'liked' : ''}`}
            onClick={handleLike}
            type="button"
            aria-label={socialInfo.stats.isLiked ? '取消点赞' : '点赞'}
          >
            <span className="action-icon">
              {socialInfo.stats.isLiked ? '❤️' : '🤍'}
            </span>
            <span className="action-text">
              {socialInfo.stats.isLiked ? '已赞' : '点赞'}
            </span>
          </button>

          <button 
            className="action-button comment-button"
            onClick={handleComment}
            type="button"
            aria-label="评论"
          >
            <span className="action-icon">💬</span>
            <span className="action-text">评论</span>
          </button>

          <button 
            className="action-button share-button"
            onClick={handleShare}
            type="button"
            aria-label="分享"
          >
            <span className="action-icon">📤</span>
            <span className="action-text">分享</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default SocialSection;