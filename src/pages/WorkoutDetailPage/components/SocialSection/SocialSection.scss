/**
 * SocialSection 样式
 * 
 * @fileoverview 社交信息区域的样式定义，符合iOS HIG标准
 * <AUTHOR> Team
 * @since 1.0.0
 */

// iOS触摸目标样式混入
@mixin ios-touch-target {
  min-width: var(--ios-touch-target); // 44px
  min-height: var(--ios-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  cursor: pointer;
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:active {
    transform: scale(0.98);
  }
}

.social-section {
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  
  // GPU加速优化
  transform: translateZ(0);
  
  .user-info {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    
    .user-avatar-container {
      position: relative;
      flex-shrink: 0;
      
      .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--border-color);
        transition: transform 0.2s ease;
        
        &:hover {
          transform: scale(1.05);
        }
      }
      
      .verified-badge {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 16px;
        height: 16px;
        background: var(--accent-500);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        border: 2px solid var(--bg-surface);
      }
    }
    
    .user-details {
      flex: 1;
      min-width: 0;
      
      .user-name-row {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        margin-bottom: var(--space-2);
        
        .user-name {
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
          margin: 0;
          
          // 文本截断
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
          margin-right: var(--space-2);
        }
        
        .post-timestamp {
          font-size: var(--text-xs);
          color: var(--text-muted);
          flex-shrink: 0;
        }
      }
      
      .follow-button {
        @include ios-touch-target;
        background: var(--accent-500);
        color: white;
        border: none;
        padding: var(--space-1) var(--space-3);
        border-radius: 16px;
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        height: 32px;
        
        &:hover {
          background: var(--accent-600);
        }
        
        &:active {
          background: var(--accent-700);
        }
      }
    }
  }
  
  .post-content {
    margin-bottom: var(--space-4);
    
    p {
      font-size: var(--text-base);
      line-height: 1.6;
      color: var(--text-primary);
      margin: 0;
      word-wrap: break-word;
    }
  }
  
  .post-images {
    display: grid;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    border-radius: 8px;
    overflow: hidden;
    
    // 单张图片
    &:has(.post-image:only-child) {
      grid-template-columns: 1fr;
      
      .post-image {
        max-height: 300px;
        object-fit: cover;
      }
    }
    
    // 多张图片网格布局
    &:has(.post-image:nth-child(2)) {
      grid-template-columns: 1fr 1fr;
    }
    
    &:has(.post-image:nth-child(4)) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
    }
    
    .post-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
      border-radius: 4px;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: scale(1.02);
      }
    }
  }
  
  .social-stats-actions {
    border-top: 1px solid var(--border-color);
    padding-top: var(--space-3);
    
    .social-stats {
      display: flex;
      gap: var(--space-4);
      margin-bottom: var(--space-3);
      
      .stat-item {
        display: flex;
        align-items: baseline;
        gap: var(--space-1);
        
        .stat-number {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
        }
        
        .stat-label {
          font-size: var(--text-xs);
          color: var(--text-secondary);
        }
      }
    }
    
    .social-actions {
      display: flex;
      gap: var(--space-2);
      
      .action-button {
        @include ios-touch-target;
        background: none;
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: var(--space-2) var(--space-3);
        display: flex;
        align-items: center;
        gap: var(--space-1);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        transition: all 0.2s ease;
        min-height: 36px;
        
        .action-icon {
          font-size: 16px;
        }
        
        .action-text {
          font-weight: var(--font-medium);
        }
        
        &:hover {
          background: var(--bg-hover);
          border-color: var(--border-hover);
          color: var(--text-primary);
        }
        
        &.like-button {
          &.liked {
            background: var(--error-50);
            border-color: var(--error-200);
            color: var(--error-600);
            
            &:hover {
              background: var(--error-100);
            }
          }
        }
        
        &.comment-button {
          &:hover {
            background: var(--info-50);
            border-color: var(--info-200);
            color: var(--info-600);
          }
        }
        
        &.share-button {
          &:hover {
            background: var(--success-50);
            border-color: var(--success-200);
            color: var(--success-600);
          }
        }
      }
    }
  }
}

// iOS专用媒体查询优化
@media only screen and (max-width: 768px) {
  .social-section {
    padding: var(--space-3);
    border-radius: 8px;
    
    .user-info {
      gap: var(--space-2);
      margin-bottom: var(--space-3);
      
      .user-avatar-container {
        .user-avatar {
          width: 40px;
          height: 40px;
        }
        
        .verified-badge {
          width: 14px;
          height: 14px;
          font-size: 9px;
        }
      }
    }
    
    .post-content {
      margin-bottom: var(--space-3);
    }
    
    .post-images {
      margin-bottom: var(--space-3);
      
      .post-image {
        height: 120px;
      }
    }
    
    .social-stats-actions {
      .social-actions {
        flex-wrap: wrap;
        
        .action-button {
          flex: 1;
          min-width: 80px;
          justify-content: center;
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .social-section {
    .user-info {
      .user-avatar-container {
        .user-avatar {
          border-color: var(--border-color-dark);
        }
        
        .verified-badge {
          border-color: var(--bg-surface-dark);
        }
      }
    }
    
    .social-stats-actions {
      .social-actions {
        .action-button {
          &.like-button.liked {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
          }
          
          &.comment-button:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
          }
          
          &.share-button:hover {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
          }
        }
      }
    }
  }
}

// 高对比度模式适配
@media (prefers-contrast: high) {
  .social-section {
    border-width: 2px;
    
    .user-info {
      .user-avatar-container {
        .user-avatar {
          border-width: 2px;
        }
      }
    }
    
    .social-stats-actions {
      .social-actions {
        .action-button {
          border-width: 2px;
        }
      }
    }
  }
}