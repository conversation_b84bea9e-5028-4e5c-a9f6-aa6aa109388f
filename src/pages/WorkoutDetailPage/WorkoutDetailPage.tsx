/**
 * WorkoutDetailPage 主页面组件
 * 
 * @fileoverview 训练详情页面的主要组件
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useMemo } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useWorkoutDetail } from './hooks/useWorkoutDetail';
import { DataSource } from './types';
import { TrainingExerciseCard } from './components/TrainingExerciseCard';
import { SocialSection } from './components/SocialSection';
import { WorkoutDetailSkeleton } from './components/WorkoutDetailSkeleton';
import { ErrorStateComponent, NotFoundState } from './components/ErrorStates';
import './WorkoutDetailPage.scss';

/**
 * WorkoutDetailPage 组件属性
 */
export interface WorkoutDetailPageProps {
  className?: string;
}

/**
 * 训练详情页面主组件
 * 支持两种业务场景：Feed社交场景和Profile纯数据场景
 */
const WorkoutDetailPage: React.FC<WorkoutDetailPageProps> = ({
  className = ''
}) => {
  const { postId, workoutId } = useParams<{ postId?: string; workoutId?: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  // 确定数据源类型
  const dataSource: DataSource = useMemo(() => {
    if (postId) {
      return { type: 'feed', id: postId };
    }
    if (workoutId) {
      return { type: 'profile', id: workoutId };
    }
    throw new Error('无效的路由参数：缺少 postId 或 workoutId');
  }, [postId, workoutId]);

  // 使用核心 Hook 获取数据
  const {
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  } = useWorkoutDetail(dataSource, location.state?.initialData);

  // 返回按钮处理
  const handleGoBack = () => {
    navigate(-1);
  };

  // 加载状态
  if (loading) {
    return (
      <WorkoutDetailSkeleton 
        showSocialSection={dataSource.type === 'feed'}
        className={className}
      />
    );
  }

  // 错误状态
  if (error) {
    return (
      <ErrorStateComponent
        error={{ 
          code: 'NETWORK_ERROR', 
          message: error, 
          retryable: true 
        }}
        onRetry={refreshData}
        onGoBack={handleGoBack}
        className={className}
      />
    );
  }

  // 数据为空
  if (!workoutDetail) {
    return (
      <NotFoundState
        dataType={dataSource.type === 'feed' ? '训练帖子' : '训练记录'}
        onGoBack={handleGoBack}
        className={className}
      />
    );
  }

  return (
    <div className={`workout-detail-page ${className}`}>
      {/* 页面头部 */}
      <header className="page-header">
        <button className="back-button" onClick={handleGoBack}>
          ← 返回
        </button>
        <h1 className="page-title">{workoutDetail.name}</h1>
        <div className="header-actions">
          {workoutDetail.source === 'feed' && (
            <button className="share-button">
              分享
            </button>
          )}
          {workoutDetail.source === 'profile' && (
            <button className="edit-button">
              编辑
            </button>
          )}
        </div>
      </header>

      {/* 内容区域 */}
      <main className="page-content">
        {/* 社交信息区域（仅Feed场景） */}
        <SocialSection 
          socialInfo={workoutDetail.socialInfo}
          onSocialAction={handleSocialAction}
        />

        {/* 训练统计 */}
        <section className="workout-stats-section">
          <h2 className="section-title">训练统计</h2>
          <div className="workout-stats">
            <div className="stat-item">
              <span className="stat-label">持续时间</span>
              <span className="stat-value">
                {Math.floor(workoutDetail.workoutStats.duration_seconds / 60)}分钟
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">总组数</span>
              <span className="stat-value">{workoutDetail.workoutStats.total_sets}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">总重量</span>
              <span className="stat-value">{workoutDetail.workoutStats.total_volume}kg</span>
            </div>
            {workoutDetail.workoutStats.calories_burned && (
              <div className="stat-item">
                <span className="stat-label">消耗卡路里</span>
                <span className="stat-value">{workoutDetail.workoutStats.calories_burned}</span>
              </div>
            )}
          </div>
        </section>

        {/* 训练动作 */}
        <section className="exercises-section">
          <h2 className="section-title">训练动作 ({workoutDetail.exercises.length})</h2>
          <div className="exercises-list">
            {workoutDetail.exercises.map(exercise => (
              <TrainingExerciseCard
                key={exercise.id}
                exercise={exercise}
                isExpanded={expandedExercises.includes(exercise.id)}
                onToggleExpansion={() => toggleExerciseExpansion(exercise.id)}
              />
            ))}
          </div>
        </section>

        {/* 肌肉详情 */}
        <section className="muscle-detail-section">
          <h2 className="section-title">目标肌群</h2>
          <div className="muscle-info">
            <p>目标肌群: {workoutDetail.muscleData.targetMuscleIds.length} 个</p>
            <p>协同肌群: {workoutDetail.muscleData.synergistMuscleIds.length} 个</p>
            {/* TODO: 集成 MuscleInfoCard 组件 */}
          </div>
        </section>
      </main>
    </div>
  );
};

export default WorkoutDetailPage;