import React, { useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar, Button } from '@heroui/react';
import { FeedFilterType } from '../../models/common/Filter';
import { WorkoutCarousel } from '../../components/fitness/WorkoutCarousel/WorkoutCarousel';
import { WorkoutStatsCard } from '../../components/fitness/WorkoutStatsCard/WorkoutStatsCard';
import { CommunityService } from '../../services/communityService';
import { formatPostTime } from '../../utils/timeFormatter';
import { ServiceCacheAdapter } from '../../services/cache/ServiceCacheAdapter';
import { useFilteredInfiniteFeed } from '../../hooks/useInfiniteFeed';
import { runIOSNetworkDiagnostics, generateDiagnosticsReport } from '../../utils/iosNetworkDiagnostics';
import './FeedPage.scss';



const FeedPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 筛选状态
  const [filter, setFilter] = useState<FeedFilterType>('all');
  
  // 使用新的无限分页Hook
  const {
    posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    mutate
  } = useFilteredInfiniteFeed(filter);

  // 获取ServiceCacheAdapter实例
  const cacheAdapter = useMemo(() => ServiceCacheAdapter.getInstance(), []);



  // 处理点赞（乐观更新 + 缓存失效）
  const handleLike = useCallback(async (postId: string) => {
    const targetPost = posts.find(post => post.id === postId);
    if (!targetPost) return;

    const wasLiked = targetPost.isLiked;
    
    try {
      console.log('【FeedPage】点赞帖子:', { postId, wasLiked });

      // 1. 乐观更新本地状态
      const optimisticPosts = posts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            isLiked: !post.isLiked,
            stats: {
              ...post.stats,
              likes: post.isLiked ? post.stats.likes - 1 : post.stats.likes + 1
            }
          };
        }
        return post;
      });

      // 立即更新UI
      await mutate(optimisticPosts);

      // 2. 调用API
      await CommunityService.togglePostLike(postId);

      // 3. 通知缓存适配器处理失效
      await cacheAdapter.handleUserLike(postId, !wasLiked);

      console.log('【FeedPage】点赞成功:', { postId, newState: !wasLiked });

    } catch (error) {
      console.error('【FeedPage】点赞失败:', error);
      
      // 4. 错误时回滚乐观更新
      await mutate(posts);
      
      // 可以添加错误提示
      // showErrorToast('点赞失败，请稍后重试');
    }
  }, [posts, mutate, cacheAdapter]);

  // 处理训练详情跳转
  const handleWorkoutDetail = useCallback((postId: string, feedPost: any) => {
    try {
      // 跳转到WorkoutDetailPage，传递postId和完整的feedPost数据
      navigate(`/workout/${postId}`, {
        state: {
          initialData: feedPost // 传递完整的FeedPost对象
        }
      });
    } catch (error) {
      console.error('【FeedPage】跳转到训练详情失败:', error);
    }
  }, [navigate]);

  // 处理关注/取消关注（乐观更新）
  const handleFollow = useCallback(async (userId: string, isCurrentlyFollowing: boolean) => {
    try {
      console.log('【FeedPage】关注状态切换:', { userId, isCurrentlyFollowing });

      // 1. 乐观更新本地状态
      const optimisticPosts = posts.map(post => {
        if (post.user.id === userId) {
          return {
            ...post,
            user: {
              ...post.user,
              isFollowing: !isCurrentlyFollowing
            }
          };
        }
        return post;
      });

      // 立即更新UI
      await mutate(optimisticPosts);

      // 2. 调用API（待实现）
      // await CommunityService.toggleFollow(userId);

      // 3. 通知缓存适配器处理失效
      await cacheAdapter.handleUserFollow(userId, !isCurrentlyFollowing);
      
      console.log(`【FeedPage】${isCurrentlyFollowing ? '取消关注' : '关注'}成功`);

    } catch (error) {
      console.error('【FeedPage】关注操作失败:', error);
      
      // 错误时回滚
      await mutate(posts);
    }
  }, [posts, mutate, cacheAdapter]);



  // 处理训练记录轮播切换
  const handleTrainingCarouselChange = useCallback((postId: string, index: number) => {
    console.log('【FeedPage】训练记录轮播切换:', { postId, index });
    // 这里可以追踪用户浏览行为
  }, []);

  // 处理筛选器变化
  const handleFilterChange = useCallback((newFilter: FeedFilterType) => {
    console.log('【FeedPage】筛选器变化:', { from: filter, to: newFilter });
    setFilter(newFilter);
  }, [filter]);

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    console.log('【FeedPage】手动刷新数据');

    try {
      await refresh();
    } catch (error) {
      console.error('【FeedPage】刷新失败，开始网络诊断:', error);

      // 如果刷新失败，运行网络诊断
      try {
        const diagnostics = await runIOSNetworkDiagnostics();
        const report = generateDiagnosticsReport(diagnostics);
        console.log('【FeedPage】网络诊断报告:\n', report);

        // 可以在这里显示用户友好的错误信息
        if (!diagnostics.connectivityTest.canReachServer) {
          console.error('【FeedPage】服务器连接失败，请检查网络设置');
        } else if (!diagnostics.httpTest.canMakeHttpRequest) {
          console.error('【FeedPage】HTTP请求失败，可能是认证或权限问题');
        } else if (!diagnostics.authTest.isAuthenticated) {
          console.error('【FeedPage】认证失败，请重新登录');
        }
      } catch (diagError) {
        console.error('【FeedPage】网络诊断失败:', diagError);
      }
    }
  }, [refresh]);

  // 加载更多
  const handleLoadMore = useCallback(async () => {
    if (!loading && hasMore) {
      console.log('【FeedPage】加载更多数据');
      await loadMore();
    }
  }, [loading, hasMore, loadMore]);

  // 过滤逻辑现在在useFilteredInfiniteFeed中处理
  const filteredPosts = posts;

  return (
    <div className="feed-page">

      
      {/* Feed Header */}
      <div className="feed-header">
        <div className="feed-title">
          <h1>动态</h1>
          <p>发现健身伙伴的最新动态</p>
        </div>

        {/* Filter Tabs */}
        <div className="feed-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => handleFilterChange('all')}
          >
            全部动态
          </button>

          <button
            className={`filter-btn ${filter === 'following' ? 'active' : ''}`}
            onClick={() => handleFilterChange('following')}
          >
            关注的人
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && posts.length === 0 && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>正在加载动态...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error.message || '加载失败，请稍后重试'}</p>
          <button className="retry-btn" onClick={handleRefresh}>
            重试
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && posts.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">📝</div>
          <p>暂无动态内容</p>
          <button className="refresh-btn" onClick={handleRefresh}>
            刷新
          </button>
        </div>
      )}



      {/* Feed Posts */}
      <div className="feed-posts">
        {filteredPosts.map(post => (
          <article key={post.id} className="feed-post">
            {/* Post Header */}
            <div className="post-header">
              {/* 左侧：用户头像 + 用户名称 + 发布时间 */}
              <div className="post-user-section">
                <Avatar
                  src={post.user.avatar}
                  alt={post.user.name}
                  size="md"
                  className="user-avatar heroui-avatar"
                />
                <div className="user-info">
                  <div className="user-name-row">
                    <span className="user-name">
                      {post.user.name}
                      {post.user.isVerified && (
                        <svg className="verified-icon" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2L13.09 5.26L16 6L13.09 6.74L12 10L10.91 6.74L8 6L10.91 5.26L12 2Z"/>
                          <path d="M12 12L13.09 15.26L16 16L13.09 16.74L12 20L10.91 16.74L8 16L10.91 15.26L12 12Z"/>
                        </svg>
                      )}
                    </span>
                  </div>
                  <div className="post-timestamp">
                    {formatPostTime(post.timestamp)}
                  </div>
                </div>
              </div>

              {/* 右侧：关注状态按钮 */}
              <div className="post-actions">
                <Button
                  variant="bordered"
                  size="sm"
                  className={`follow-btn heroui-button ${post.user.isFollowing ? 'following' : 'not-following'}`}
                  aria-label={post.user.isFollowing ? '已关注' : '关注'}
                  onClick={() => handleFollow(post.user.id, post.user.isFollowing || false)}
                >
                  {post.user.isFollowing ? '已关注' : '关注'}
                </Button>
              </div>
            </div>

            {/* Post Content */}
            <div className="post-content">
              {post.content.text && (
                <p className="post-text">{post.content.text}</p>
              )}

              {/* 训练统计信息 - 使用独立的WorkoutStatsCard组件 */}
              {post.content.workout && (
                <WorkoutStatsCard
                  workout={post.content.workout}
                  className="feed-workout-stats"
                  showDividers={true}
                  stats={['duration', 'weight', 'calories']}
                  onClick={() => handleWorkoutDetail(post.id, post)}
                  clickable={true}
                />
              )}

                              {/* 训练记录轮播图 - 使用WorkoutCarousel组件 */}
                {post.content.workout && post.content.carousel_items && post.content.carousel_items.length > 0 && (
                  <WorkoutCarousel
                    items={[
                      ...post.content.carousel_items,
                      ...(post.images || []).map((imageUrl, index) => ({
                        id: `user-image-${index}`,
                        type: 'user_image' as const,
                        content: {
                          image_data: {
                            url: imageUrl,
                            alt: `用户上传图片 ${index + 1}`,
                            caption: '用户分享'
                          }
                        }
                      }))
                    ]}
                    autoPlay={false}
                    showIndicators={true}
                    className="workout-carousel-wrapper"
                    onItemChange={(index: number) => handleTrainingCarouselChange(post.id, index)}
                  />
                )}
            </div>

            {/* Post Actions */}
            <div className="post-actions">
              <button 
                className={`action-btn like-btn ${post.isLiked ? 'liked' : ''}`}
                onClick={() => handleLike(post.id)}
              >
                <svg viewBox="0 0 24 24" fill={post.isLiked ? "currentColor" : "none"} stroke="currentColor">
                  <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
                </svg>
                <span>{post.stats.likes}</span>
              </button>
              
              <button className="action-btn comment-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"/>
                </svg>
                <span>{post.stats.comments}</span>
              </button>
              
              <button className="action-btn share-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="18" cy="5" r="3"/>
                  <circle cx="6" cy="12" r="3"/>
                  <circle cx="18" cy="19" r="3"/>
                  <path d="M8.59 13.51L15.42 17.49"/>
                  <path d="M15.41 6.51L8.59 10.49"/>
                </svg>
                <span>{post.stats.shares}</span>
              </button>
            </div>
          </article>
        ))}
      </div>

      {/* Load More */}
      {hasMore && !loading && posts.length > 0 && (
        <div className="load-more">
          <button className="load-more-btn" onClick={handleLoadMore}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            查看更多动态
          </button>
        </div>
      )}

      {/* Loading More */}
      {loading && posts.length > 0 && (
        <div className="loading-more">
          <div className="loading-spinner small"></div>
          <p>加载更多...</p>
        </div>
      )}
    </div>
  );
};

export default FeedPage; 