import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../../common/Icon';
import { IconName } from '../../../utils/iconMapping';
import { useBottomNavVisibility, BottomNavVisibilityEnsurer } from '../../../hooks/useBottomNavVisibility';
import './BottomNavigation.scss';

interface NavigationItem {
  id: string;
  label: string;
  iconName: IconName;
  path: string;
  badge?: number;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: '仪表板',
    iconName: 'dashboard',
    path: '/'
  },
  {
    id: 'workout',
    label: '训练',
    iconName: 'workout',
    path: '/workout'
  },
  {
    id: 'feed',
    label: '动态',
    iconName: 'feed',
    path: '/feed'
  },
  {
    id: 'routines',
    label: '计划',
    iconName: 'routines',
    path: '/routines'
  },
  {
    id: 'exercises',
    label: '动作库',
    iconName: 'exercises',
    path: '/exercises'
  },
  {
    id: 'profile',
    label: '个人',
    iconName: 'profile',
    path: '/profile'
  }
];

interface BottomNavigationProps {
  className?: string;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ className = '' }) => {
  const location = useLocation();
  const navigate = useNavigate();

  // 🔧 集成可见性管理 - 简化配置，减少重复初始化
  const { isVisible, isMounted } = useBottomNavVisibility({
    forceVisible: true,
    enableAutoFix: true
  });

  const isActivePath = (path: string): boolean => {
    return location.pathname === path;
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  // 🔧 简化的调试信息 - 减少useEffect数量
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📱 底部导航组件状态:', {
        isVisible,
        isMounted,
        pathname: location.pathname,
        timestamp: new Date().toISOString()
      });
    }
  }, [isVisible, isMounted, location.pathname]);

  return (
    <BottomNavVisibilityEnsurer>
      <nav
        className={`bottom-navigation ${className}`}
        data-visible={isVisible}
        data-mounted={isMounted}
        data-react-mounted="true"
      >
        <div className="bottom-nav-container">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              className={`bottom-nav-item ${isActivePath(item.path) ? 'active' : ''}`}
              onClick={() => handleNavigation(item.path)}
              aria-label={item.label}
            >
              <span className="bottom-nav-icon">
                <Icon name={item.iconName} size={isActivePath(item.path) ? 'medium' : 'small'} />
              </span>
              <span className="bottom-nav-label">{item.label}</span>
              {item.badge && (
                <span className="bottom-nav-badge">{item.badge}</span>
              )}
            </button>
          ))}
        </div>
      </nav>
    </BottomNavVisibilityEnsurer>
  );
};

export default BottomNavigation;
