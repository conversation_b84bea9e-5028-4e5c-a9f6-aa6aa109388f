@use '../../../styles/variables' as *;

// 底部导航样式 - iOS Safe Area优化版
.bottom-navigation {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;

  /* 🔧 修复：统一使用calc()方式，预设Safe Area值避免初始化延迟 */
  height: calc(60px + env(safe-area-inset-bottom, 34px)) !important;

  /* 🔧 修复：移除单独的padding-bottom，避免重复计算 */
  padding-bottom: 0 !important;

  background: var(--bg-surface) !important;
  border-top: 1px solid var(--primary-500, rgba(71, 85, 105, 0.3)) !important;
  z-index: 9999 !important; /* 超高优先级确保显示 */
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
  /* 确保在所有设备上显示 */
  display: block !important;
  width: 100vw !important;

  /* 🔧 Safe Area背景延伸，确保视觉连续性 */
  &::after {
    content: '';
    position: absolute;
    bottom: calc(-1 * env(safe-area-inset-bottom, 34px));
    left: 0;
    right: 0;
    height: env(safe-area-inset-bottom, 34px);
    background: inherit;
    border-top: inherit;
    z-index: -1;
  }
}

.bottom-nav-container {
  display: flex !important;
  /* ✅ iOS修复：更紧凑的按钮区域，为中间内容留出更多空间 */  
  height: 50px !important;
  align-items: center !important;
  justify-content: space-around !important;
  padding: 0 var(--space-2, 0.5rem) !important;
  position: relative !important;
  /* 居中定位，减小间距为中间内容让出空间 */
  margin-top: 5px !important;
}

.bottom-nav-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: var(--space-2, 0.5rem) !important;
  background: none !important;
  border: none !important;
  color: var(--text-secondary, #cbd5e1) !important;
  cursor: pointer !important;
  border-radius: var(--radius-md, 6px) !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  min-width: 60px !important;
  gap: 0.25rem !important;
  flex: 1 !important;
  max-width: 80px !important;

  // 悬停效果：只改变文字颜色，不设置背景色
  &:hover:not(.active) {
    color: var(--text-primary, #f8fafc) !important;
  }

  // 选中状态：只使用强调色，没有背景色
  &.active {
    color: var(--accent-500, #3b82f6) !important;
    
    .bottom-nav-icon {
      .hn {
        transform: scale(1.1) !important;
        color: var(--accent-500, #3b82f6) !important;
      }
    }
  }
}

.bottom-nav-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-bottom: 4px !important;
  
  .hn {
    transition: all var(--transition-normal) !important;
    font-size: 18px !important;
    width: 22px !important;
    height: 22px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

.bottom-nav-label {
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

.bottom-nav-badge {
  position: absolute !important;
  top: 0.25rem !important;
  right: 0.25rem !important;
  background: var(--error-500, #ef4444) !important;
  color: white !important;
  font-size: 0.625rem !important;
  font-weight: 600 !important;
  padding: 0 !important;
  min-width: 16px !important;
  height: 16px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

// 主题适配
.theme-dark .bottom-navigation {
  background: var(--bg-surface, #1e293b) !important;
  border-top-color: var(--border-color, #374151) !important;
}

.theme-light .bottom-navigation {
  background: var(--bg-surface, #ffffff) !important;
  border-top-color: var(--border-color, #e5e7eb) !important;
}

// 移动端强制显示和iOS适配
@media (max-width: 768px) {
  .bottom-navigation {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    /* 确保不被任何内容覆盖 */
    position: fixed !important;
    z-index: 10000 !important;
  }
  
  /* 🔧 iOS导航栏高度 - 使用预设Safe Area值确保初始化正确 */
  .bottom-navigation {
    height: calc(60px + env(safe-area-inset-bottom, 34px)) !important;
  }
  
     /* 🔧 横屏模式适配 - 预设Safe Area值 */
   @media (orientation: landscape) {
     .bottom-navigation {
       height: calc(50px + env(safe-area-inset-bottom, 34px)) !important;
     }
     
     .bottom-nav-container {
       height: 42px !important; /* 横屏时更加紧凑 */
       margin-top: 4px !important; /* 减小间距 */
     }
     
     .bottom-nav-item {
       padding: var(--space-1, 0.25rem) !important;
       min-height: 38px !important; /* 横屏时减小 */
     }
     
     .bottom-nav-label {
       font-size: 0.7rem !important;
     }
   }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .bottom-nav-item {
    min-width: 50px !important;
    padding: var(--space-1, 0.25rem) !important;
    min-height: 40px !important; /* 超小屏幕时紧凑设计 */
    
    .bottom-nav-icon .hn {
      font-size: 16px !important;
      width: 20px !important;
      height: 20px !important;
    }
  }
  
  .bottom-nav-label {
    font-size: 0.65rem !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .bottom-navigation {
    border-top-width: 2px !important;
  }
  
  .bottom-nav-item {
    border: 1px solid transparent !important;
    
    &:hover,
    &.active {
      border-color: currentColor !important;
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .bottom-nav-item {
    transition: none !important;
    
    .bottom-nav-icon .hn {
      transition: none !important;
    }
  }
}

/* 调试模式 - 可视化底部导航 */
.debug-bottom-nav .bottom-navigation {
  background: rgba(255, 0, 0, 0.5) !important;
  border: 3px solid red !important;
} 