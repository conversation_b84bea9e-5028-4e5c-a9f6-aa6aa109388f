/**
 * 训练服务相关类型定义
 * 支持iOS优化和完整的TypeScript类型安全
 */

// ==================== 基础类型 ====================

export type WorkoutStatus = 'not_started' | 'in_progress' | 'completed' | 'cancelled' | 'paused';
export type TrainingPlanStatus = 'active' | 'completed' | 'paused';
export type TemplateVisibility = 'everyone' | 'friends' | 'private';
export type TrainingScenario = 'gym' | 'home' | 'outdoor';

// ==================== 训练相关接口 ====================

export interface WorkoutStatistics {
  workout_id: number;
  status: WorkoutStatus;
  estimated_duration: number;
  actual_duration: number;
  net_duration: number;
  total_pause_time: number;
  pause_count: number;
  time_efficiency: number;
}

export interface WorkoutSession {
  workout_id: number;
  session_id: number;
  session_name: string;
  status: string;
  start_time: string;
  end_time: string;
}

export interface SetRecord {
  id?: number;
  training_record_id?: number;
  workout_exercise_id: number;
  set_number: number;
  reps: number;
  weight: number;
  completed: boolean;
  rpe?: number; // Rate of Perceived Exertion (1-10)
  notes?: string;
}

export interface WorkoutSetParams {
  weight: number;
  reps: number;
  rpe?: number;
  notes?: string;
}

export interface WorkoutCompletionParams {
  feedback?: string;
  difficulty_rating?: number; // 1-5
  mood_rating?: number; // 1-5
  notes?: string;
}

export interface WorkoutHistoryParams {
  user_id?: number;
  start_date?: string;
  end_date?: string;
  skip?: number;
  limit?: number;
}

// ==================== 训练计划相关接口 ====================

export interface TrainingPlanGenRequest {
  user_id: number;
  duration_weeks: number;
  days_per_week: number;
  fitness_goal: string;
  available_equipment: string[];
  focus_body_parts: string[];
  time_per_workout: number;
  additional_notes?: string;
}

export interface DailyWorkoutGenRequest {
  user_id: number;
  available_time: number;
  target_body_parts: string[];
  available_equipment: string[];
  recovery_level: 'low' | 'moderate' | 'high';
  additional_notes?: string;
}

export interface TrainingPlanBase {
  id: number;
  user_id: number;
  name: string;
  status: TrainingPlanStatus;
  is_active: boolean;
  is_template: boolean;
  privacy_setting: number; // 0: Public, 1: Private
  created_at: string;
  updated_at: string;
}

export interface WorkoutExercise {
  id: number;
  workout_id: number;
  exercise_id: number;
  exercise_name: string;
  sets: number;
  reps: number;
  weight?: number;
  rest_time: number;
  notes?: string;
  order: number;
}

export interface Workout {
  id: number;
  training_plan_id: number;
  day: number;
  notes?: string;
  exercises: WorkoutExercise[];
  scheduled_date?: string;
  status?: WorkoutStatus;
}

export interface TrainingPlanDetail extends TrainingPlanBase {
  workouts: Workout[];
  duration_weeks: number;
  days_per_week: number;
  fitness_goal: string;
  available_equipment: string[];
  focus_body_parts: string[];
  time_per_workout: number;
  additional_notes?: string;
}

// ==================== 训练模板相关接口 ====================

export interface WorkoutTemplate {
  id: number;
  user_id: number;
  name: string;
  description: string;
  estimated_duration: number;
  target_body_parts: number[];
  training_scenario: TrainingScenario;
  notes: string;
  visibility: TemplateVisibility;
  exercise_count: number;
  exercises: WorkoutExercise[];
  created_at: string;
  updated_at: string;
}

export interface WorkoutTemplateCreateParams {
  name: string;
  description?: string;
  estimated_duration?: number;
  target_body_parts?: number[];
  training_scenario?: TrainingScenario;
  notes?: string;
  visibility?: TemplateVisibility;
  exercises: Omit<WorkoutExercise, 'id' | 'workout_id'>[];
}

export interface WorkoutTemplateApplyParams {
  date?: string; // YYYY-MM-DD format
}

// ==================== 训练记录相关接口 ====================

export interface TrainingRecord {
  id: number;
  user_id: number;
  workout_id: number;
  date: string;
  duration: number;
  notes?: string;
  set_records: SetRecord[];
}

export interface TrainingPlanUpdate {
  plan_data?: Partial<TrainingPlanBase>;
  workout_data?: Partial<Workout>[];
  workout_exercise_data?: Partial<WorkoutExercise>[];
  training_record_data?: Partial<TrainingRecord>[];
  set_record_data?: Partial<SetRecord>[];
}

// ==================== API响应接口 ====================

export interface ApiWorkoutResponse {
  id: number;
  training_plan_id: number;
  scheduled_date: string;
  status: WorkoutStatus;
  created_at: string;
  updated_at: string;
  exercises?: WorkoutExercise[];
  statistics?: WorkoutStatistics;
}

export interface ApiTrainingPlanResponse extends TrainingPlanDetail {}

export interface ApiWorkoutTemplateResponse extends WorkoutTemplate {}

// ==================== 缓存相关接口 ====================

export interface WorkoutCacheData {
  workouts: ApiWorkoutResponse[];
  lastUpdated: number;
  hasMore: boolean;
}

export interface TrainingPlanCacheData {
  plans: TrainingPlanBase[];
  lastUpdated: number;
  hasMore: boolean;
}

// ==================== 分页和筛选接口 ====================

export interface WorkoutListParams {
  skip?: number;
  limit?: number;
  status?: WorkoutStatus;
  date_from?: string;
  date_to?: string;
}

export interface TrainingPlanListParams {
  skip?: number;
  limit?: number;
  active_only?: boolean;
  include_templates?: boolean;
}

export interface WorkoutPage {
  workouts: ApiWorkoutResponse[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

export interface TrainingPlanPage {
  plans: TrainingPlanBase[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// ==================== iOS优化相关接口 ====================

export interface iOSWorkoutFeedback {
  hapticStyle: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
}

export interface WorkoutTimerState {
  isRunning: boolean;
  startTime?: number;
  pausedTime?: number;
  totalPausedDuration: number;
  currentDuration: number;
}

// ==================== 错误处理接口 ====================

export interface WorkoutServiceError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
}

export interface WorkoutOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: WorkoutServiceError;
}
