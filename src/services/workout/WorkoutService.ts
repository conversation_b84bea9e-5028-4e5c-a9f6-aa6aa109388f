/**
 * 训练服务模块 - 核心训练执行管理
 * 提供完整的训练生命周期管理，包含iOS原生优化
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  WorkoutStatistics,
  WorkoutSetParams,
  WorkoutCompletionParams,
  WorkoutHistoryParams,
  WorkoutPage,
  ApiWorkoutResponse,
  WorkoutOperationResult,
  iOSWorkoutFeedback
} from './types';

class WorkoutService {
  private apiClient: ApiClient;
  private cacheManager: CacheManager | null = null;
  private readonly pageSize = 20;

  // 缓存配置
  private readonly CACHE_CONFIG = {
    WORKOUTS: {
      ttl: 2 * 60 * 60 * 1000,      // 2小时
      tags: ['workout', 'training'],
      priority: 'high' as const,
      persistent: true
    },
    WORKOUT_DETAIL: {
      ttl: 30 * 60 * 1000,          // 30分钟
      tags: ['workout', 'detail'],
      priority: 'high' as const,
      persistent: true
    },
    WORKOUT_HISTORY: {
      ttl: 60 * 60 * 1000,          // 1小时
      tags: ['workout', 'history'],
      priority: 'medium' as const,
      persistent: true
    },
    CURRENT_WORKOUT: {
      ttl: 0,                       // 无过期时间
      tags: ['workout', 'current'],
      priority: 'high' as const,
      persistent: true
    }
  };

  constructor() {
    // 初始化API客户端
    this.apiClient = new ApiClient();

    // 异步初始化缓存管理器
    this.initializeCacheManager();

    console.log('[WorkoutService] 训练服务初始化完成');
  }

  /**
   * 异步初始化缓存管理器
   */
  private async initializeCacheManager(): Promise<void> {
    try {
      this.cacheManager = await GlobalCacheManager.getInstance();
      console.log('[WorkoutService] ✅ 全局缓存管理器连接成功');
    } catch (error) {
      console.error('[WorkoutService] ❌ 缓存管理器初始化失败:', error);
    }
  }

  /**
   * 获取缓存管理器（确保已初始化）
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(resource: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('workout')
      .resource(resource)
      .params(params)
      .version('2.0')
      .build();
  }

  /**
   * iOS触觉反馈
   */
  private async provideiOSFeedback(feedback: iOSWorkoutFeedback): Promise<void> {
    if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
      return;
    }

    try {
      const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
      
      // 触觉反馈
      switch (feedback.hapticStyle) {
        case 'light':
          await Haptics.impact({ style: ImpactStyle.Light });
          break;
        case 'medium':
          await Haptics.impact({ style: ImpactStyle.Medium });
          break;
        case 'heavy':
          await Haptics.impact({ style: ImpactStyle.Heavy });
          break;
      }

      // 状态栏样式更新
      if (feedback.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = feedback.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

    } catch (error) {
      console.warn('⚠️ [WorkoutService] iOS反馈失败:', error);
    }
  }

  /**
   * 开始训练（iOS优化）
   */
  public async startWorkout(workoutId: string | number): Promise<WorkoutOperationResult<ApiWorkoutResponse>> {
    try {
      console.log('[WorkoutService] 开始训练:', { workoutId });

      const response = await this.apiClient.post<ApiWorkoutResponse>(
        `/api/v1/workout/${workoutId}/start`
      );

      // iOS触觉反馈 - 训练开始
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        statusBarStyle: 'dark', // 专注模式
        notificationTitle: '训练开始',
        notificationBody: '专注训练，加油！'
      });

      // 缓存当前训练状态
      const cacheKey = this.generateCacheKey('current', { workoutId });
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CURRENT_WORKOUT);

      // 失效相关缓存
      await this.invalidateWorkoutCache(workoutId.toString());

      console.log('✅ [WorkoutService] 训练开始成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 开始训练失败:', error);
      return {
        success: false,
        error: {
          code: 'START_WORKOUT_FAILED',
          message: error instanceof Error ? error.message : '开始训练失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 暂停训练（iOS优化）
   */
  public async pauseWorkout(workoutId: string | number): Promise<WorkoutOperationResult<ApiWorkoutResponse>> {
    try {
      console.log('[WorkoutService] 暂停训练:', { workoutId });

      const response = await this.apiClient.post<ApiWorkoutResponse>(
        `/api/v1/workout/${workoutId}/pause`
      );

      // iOS触觉反馈 - 训练暂停
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        statusBarStyle: 'light', // 暂停状态
        notificationTitle: '训练暂停',
        notificationBody: '休息一下，稍后继续'
      });

      // 更新当前训练缓存
      const cacheKey = this.generateCacheKey('current', { workoutId });
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CURRENT_WORKOUT);

      console.log('✅ [WorkoutService] 训练暂停成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 暂停训练失败:', error);
      return {
        success: false,
        error: {
          code: 'PAUSE_WORKOUT_FAILED',
          message: error instanceof Error ? error.message : '暂停训练失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 恢复训练（iOS优化）
   */
  public async resumeWorkout(workoutId: string | number): Promise<WorkoutOperationResult<ApiWorkoutResponse>> {
    try {
      console.log('[WorkoutService] 恢复训练:', { workoutId });

      const response = await this.apiClient.post<ApiWorkoutResponse>(
        `/api/v1/workout/${workoutId}/resume`
      );

      // iOS触觉反馈 - 训练恢复
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        statusBarStyle: 'dark', // 回到专注模式
        notificationTitle: '训练恢复',
        notificationBody: '继续加油！'
      });

      // 更新当前训练缓存
      const cacheKey = this.generateCacheKey('current', { workoutId });
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CURRENT_WORKOUT);

      console.log('✅ [WorkoutService] 训练恢复成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 恢复训练失败:', error);
      return {
        success: false,
        error: {
          code: 'RESUME_WORKOUT_FAILED',
          message: error instanceof Error ? error.message : '恢复训练失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 完成训练（iOS优化）
   */
  public async completeWorkout(
    workoutId: string | number,
    params: WorkoutCompletionParams = {}
  ): Promise<WorkoutOperationResult<ApiWorkoutResponse>> {
    try {
      console.log('[WorkoutService] 完成训练:', { workoutId, params });

      const response = await this.apiClient.post<ApiWorkoutResponse>(
        `/api/v1/workout/${workoutId}/complete`,
        params
      );

      // iOS触觉反馈 - 训练完成
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        statusBarStyle: 'light', // 恢复正常状态
        notificationTitle: '训练完成！',
        notificationBody: '恭喜你完成了今天的训练！'
      });

      // 清除当前训练缓存
      const currentCacheKey = this.generateCacheKey('current', { workoutId });
      const cacheManager = await this.getCacheManager();
      await cacheManager.delete(currentCacheKey);

      // 失效相关缓存
      await this.invalidateWorkoutCache(workoutId.toString());

      console.log('✅ [WorkoutService] 训练完成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 完成训练失败:', error);
      return {
        success: false,
        error: {
          code: 'COMPLETE_WORKOUT_FAILED',
          message: error instanceof Error ? error.message : '完成训练失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 记录训练组数（iOS优化）
   */
  public async recordWorkoutSet(
    workoutId: string | number,
    exerciseId: string | number,
    params: WorkoutSetParams
  ): Promise<WorkoutOperationResult<any>> {
    try {
      console.log('[WorkoutService] 记录训练组数:', { workoutId, exerciseId, params });

      const response = await this.apiClient.post(
        `/api/v1/workout/${workoutId}/exercises/${exerciseId}/records`,
        params
      );

      // iOS触觉反馈 - 记录组数
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '组数记录',
        notificationBody: `${params.weight}kg × ${params.reps}次`
      });

      console.log('✅ [WorkoutService] 训练组数记录成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 记录训练组数失败:', error);
      return {
        success: false,
        error: {
          code: 'RECORD_SET_FAILED',
          message: error instanceof Error ? error.message : '记录训练组数失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练详情（带缓存）
   */
  public async getWorkoutDetail(
    workoutId: string | number,
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<ApiWorkoutResponse>> {
    const cacheKey = this.generateCacheKey('detail', { workoutId });

    try {
      console.log('[WorkoutService] 获取训练详情:', { workoutId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedDetail = await cacheManager.get<ApiWorkoutResponse>(cacheKey);
        if (cachedDetail) {
          console.log('🎯 [WorkoutService] 训练详情缓存命中');
          return {
            success: true,
            data: cachedDetail
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<ApiWorkoutResponse>(
        `/api/v1/workout/${workoutId}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.WORKOUT_DETAIL);

      console.log('✅ [WorkoutService] 训练详情获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 获取训练详情失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_WORKOUT_DETAIL_FAILED',
          message: error instanceof Error ? error.message : '获取训练详情失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练统计
   */
  public async getWorkoutStatistics(
    workoutId: string | number
  ): Promise<WorkoutOperationResult<WorkoutStatistics>> {
    try {
      console.log('[WorkoutService] 获取训练统计:', { workoutId });

      const response = await this.apiClient.get<WorkoutStatistics>(
        `/api/v1/workout/${workoutId}/statistics`
      );

      console.log('✅ [WorkoutService] 训练统计获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 获取训练统计失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_WORKOUT_STATISTICS_FAILED',
          message: error instanceof Error ? error.message : '获取训练统计失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练历史（带缓存和分页）
   */
  public async getWorkoutHistory(
    params: WorkoutHistoryParams = {},
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<WorkoutPage>> {
    const cacheKey = this.generateCacheKey('history', params);

    try {
      console.log('[WorkoutService] 获取训练历史:', { params, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedHistory = await cacheManager.get<WorkoutPage>(cacheKey);
        if (cachedHistory) {
          console.log('🎯 [WorkoutService] 训练历史缓存命中');
          return {
            success: true,
            data: cachedHistory
          };
        }
      }

      // 设置默认参数
      const queryParams = {
        skip: String(0),
        limit: String(this.pageSize),
        ...params
      };

      // 从API获取
      const workouts = await this.apiClient.get<ApiWorkoutResponse[]>(
        '/api/v1/workout/history',
        { params: queryParams }
      );

      const response: WorkoutPage = {
        workouts,
        hasMore: workouts.length === Number(queryParams.limit),
        total: workouts.length,
        currentPage: Math.floor(Number(queryParams.skip) / Number(queryParams.limit))
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.WORKOUT_HISTORY);

      console.log('✅ [WorkoutService] 训练历史获取成功:', {
        count: workouts.length,
        hasMore: response.hasMore
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutService] 获取训练历史失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_WORKOUT_HISTORY_FAILED',
          message: error instanceof Error ? error.message : '获取训练历史失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效训练相关缓存
   */
  private async invalidateWorkoutCache(workoutId?: string): Promise<void> {
    const cacheManager = await this.getCacheManager();
    if (workoutId) {
      const pattern = `workout:*workoutId=${workoutId}*`;
      await cacheManager.clear(pattern);
    } else {
      await cacheManager.invalidate(['workout', 'training']);
    }
    console.log('♻️ [WorkoutService] 训练缓存已失效');
  }

  /**
   * 获取缓存管理器实例
   */
  public async getCacheManagerInstance(): Promise<CacheManager> {
    return await this.getCacheManager();
  }
}

// 导出单例实例
export const workoutService = new WorkoutService();
export default workoutService;
