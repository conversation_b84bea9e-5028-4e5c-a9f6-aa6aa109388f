import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { HeroUIProvider } from '@heroui/react'
import App from './App'
import { ThemeProvider } from './contexts/ThemeContext'
import './styles/global.scss'

import './styles/mobile-layout.scss'

// 🔧 Safe Area监控 - 仅在开发环境启用
import { autoMonitorSafeArea } from './utils/safeAreaMonitor'

// 启动Safe Area监控
autoMonitorSafeArea();


const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
)

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <HeroUIProvider>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </HeroUIProvider>
    </BrowserRouter>
  </React.StrictMode>
)