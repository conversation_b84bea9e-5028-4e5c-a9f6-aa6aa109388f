import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { HeroUIProvider } from '@heroui/react'
import App from './App'
import { ThemeProvider } from './contexts/ThemeContext'
import './styles/global.scss'

import './styles/mobile-layout.scss'

// 🔧 Safe Area监控 - 仅在开发环境启用
import { autoMonitorSafeArea } from './utils/safeAreaMonitor'
// 🔧 底部导航可见性监控 - 仅在开发环境启用
import { autoMonitorBottomNavVisibility } from './utils/bottomNavVisibilityMonitor'

// 启动Safe Area监控
autoMonitorSafeArea();
// 启动底部导航可见性监控
autoMonitorBottomNavVisibility();


const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
)

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <HeroUIProvider>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </HeroUIProvider>
    </BrowserRouter>
  </React.StrictMode>
)